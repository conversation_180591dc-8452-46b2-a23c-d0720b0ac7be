import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/function_process.dart';
import '../providers/function_processes_providers.dart';

class AddFunctionProcessDialog extends ConsumerStatefulWidget {
  final FunctionProcess? process; // For editing
  final VoidCallback? onProcessAdded;

  const AddFunctionProcessDialog({
    super.key,
    this.process,
    this.onProcessAdded,
  });

  @override
  ConsumerState<AddFunctionProcessDialog> createState() => _AddFunctionProcessDialogState();
}

class _AddFunctionProcessDialogState extends ConsumerState<AddFunctionProcessDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedCategory = 'maintenance';
  String _selectedPriority = 'medium';
  String _selectedStatus = 'active';
  String _selectedFrequency = 'daily';
  bool _isAutomated = true;
  bool _isLoading = false;

  final List<String> _categories = [
    'maintenance',
    'monitoring',
    'backup',
    'security',
    'reporting',
    'notification',
    'cleanup',
    'sync',
  ];

  final List<String> _priorities = [
    'critical',
    'high',
    'medium',
    'low',
  ];

  final List<String> _statuses = [
    'active',
    'inactive',
    'maintenance',
  ];

  final List<String> _frequencies = [
    'hourly',
    'daily',
    'weekly',
    'monthly',
    'on-demand',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.process != null) {
      _populateFields();
    }
  }

  void _populateFields() {
    final process = widget.process!;
    _nameController.text = process.name;
    _descriptionController.text = process.description ?? '';
    _selectedCategory = process.category;
    _selectedPriority = process.priority;
    _selectedStatus = process.status;
    _selectedFrequency = process.executionFrequency ?? 'daily';
    _isAutomated = process.isAutomated;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.process != null;

    return AlertDialog(
      title: Text(isEditing ? 'Edit Function Process' : 'Add Function Process'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Name
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Process Name *',
                    hintText: 'e.g., Daily Backup Process',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Process name is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    hintText: 'Optional description of the process',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Category and Priority Row
                Row(
                  children: [
                    // Category
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'Category *',
                          border: OutlineInputBorder(),
                        ),
                        items: _categories.map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Row(
                              children: [
                                Icon(_getCategoryIcon(category), size: 20),
                                const SizedBox(width: 8),
                                Text(_getCategoryDisplayName(category)),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),

                    // Priority
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPriority,
                        decoration: const InputDecoration(
                          labelText: 'Priority *',
                          border: OutlineInputBorder(),
                        ),
                        items: _priorities.map((priority) {
                          return DropdownMenuItem(
                            value: priority,
                            child: Text(_getPriorityDisplayName(priority)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedPriority = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Status
                DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(
                    labelText: 'Status *',
                    border: OutlineInputBorder(),
                  ),
                  items: _statuses.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(_getStatusDisplayName(status)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Automation Toggle
                SwitchListTile(
                  title: const Text('Automated Process'),
                  subtitle: Text(_isAutomated 
                      ? 'Process runs automatically' 
                      : 'Process requires manual execution'),
                  value: _isAutomated,
                  onChanged: (value) {
                    setState(() {
                      _isAutomated = value;
                    });
                  },
                ),

                // Frequency (only if automated)
                if (_isAutomated) ...[
                  const SizedBox(height: AppConstants.defaultPadding),
                  DropdownButtonFormField<String>(
                    value: _selectedFrequency,
                    decoration: const InputDecoration(
                      labelText: 'Execution Frequency',
                      border: OutlineInputBorder(),
                    ),
                    items: _frequencies.map((frequency) {
                      return DropdownMenuItem(
                        value: frequency,
                        child: Text(_getFrequencyDisplayName(frequency)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedFrequency = value!;
                      });
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveProcess,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'maintenance':
        return Icons.build;
      case 'monitoring':
        return Icons.monitor;
      case 'backup':
        return Icons.backup;
      case 'security':
        return Icons.security;
      case 'reporting':
        return Icons.assessment;
      case 'notification':
        return Icons.notifications;
      case 'cleanup':
        return Icons.cleaning_services;
      case 'sync':
        return Icons.sync;
      default:
        return Icons.settings;
    }
  }

  String _getCategoryDisplayName(String category) {
    switch (category.toLowerCase()) {
      case 'maintenance':
        return 'Maintenance';
      case 'monitoring':
        return 'Monitoring';
      case 'backup':
        return 'Backup';
      case 'security':
        return 'Security';
      case 'reporting':
        return 'Reporting';
      case 'notification':
        return 'Notification';
      case 'cleanup':
        return 'Cleanup';
      case 'sync':
        return 'Synchronization';
      default:
        return category;
    }
  }

  String _getPriorityDisplayName(String priority) {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'Critical';
      case 'high':
        return 'High';
      case 'medium':
        return 'Medium';
      case 'low':
        return 'Low';
      default:
        return priority;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'maintenance':
        return 'Under Maintenance';
      default:
        return status;
    }
  }

  String _getFrequencyDisplayName(String frequency) {
    switch (frequency.toLowerCase()) {
      case 'hourly':
        return 'Every hour';
      case 'daily':
        return 'Daily';
      case 'weekly':
        return 'Weekly';
      case 'monthly':
        return 'Monthly';
      case 'on-demand':
        return 'On demand';
      default:
        return frequency;
    }
  }

  Future<void> _saveProcess() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.process != null) {
        // Update existing process
        final request = UpdateFunctionProcessRequest(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          category: _selectedCategory,
          priority: _selectedPriority,
          status: _selectedStatus,
          isAutomated: _isAutomated,
          executionFrequency: _isAutomated ? _selectedFrequency : null,
        );

        await ref.read(functionProcessesNotifierProvider.notifier)
            .updateFunctionProcess(widget.process!.id, request);
      } else {
        // Create new process
        final request = CreateFunctionProcessRequest(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          category: _selectedCategory,
          priority: _selectedPriority,
          status: _selectedStatus,
          isAutomated: _isAutomated,
          executionFrequency: _isAutomated ? _selectedFrequency : null,
        );

        await ref.read(functionProcessesNotifierProvider.notifier)
            .addFunctionProcess(request);
      }

      if (mounted) {
        Navigator.of(context).pop();
        widget.onProcessAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to save function process: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
