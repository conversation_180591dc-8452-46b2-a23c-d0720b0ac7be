import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/dynamic_role_based_widget.dart';
import '../../../../core/utils/app_utils.dart';
import '../providers/screen_management_providers.dart';
import '../widgets/screen_card.dart';
import '../widgets/widget_card.dart';
import '../widgets/create_screen_dialog.dart';
import '../widgets/create_widget_dialog.dart';
import '../../data/models/custom_screen.dart';

class ScreenManagementScreen extends ConsumerStatefulWidget {
  const ScreenManagementScreen({super.key});

  @override
  ConsumerState<ScreenManagementScreen> createState() => _ScreenManagementScreenState();
}

class _ScreenManagementScreenState extends ConsumerState<ScreenManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Screen & Widget Management'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Custom Screens', icon: Icon(Icons.web)),
            Tab(text: 'Widget Library', icon: Icon(Icons.widgets)),
          ],
        ),
        actions: [
          DynamicRoleBasedWidget(
            requiredPermissions: const ['screens.manage'],
            child: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                ref.invalidate(customScreensProvider);
                ref.invalidate(customWidgetsProvider);
              },
              tooltip: 'Refresh',
            ),
          ),
        ],
      ),
      body: DynamicRoleBasedWidget(
        requiredPermissions: const ['screens.read'],
        fallback: _buildNoPermissionView(),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildScreensTab(),
            _buildWidgetsTab(),
          ],
        ),
      ),
      floatingActionButton: DynamicRoleBasedFAB(
        requiredPermissions: const ['screens.manage'],
        onPressed: () => _showCreateDialog(),
        tooltip: 'Create New',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildNoPermissionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You don\'t have permission to manage screens and widgets.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildScreensTab() {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildScreensList(),
        ),
      ],
    );
  }

  Widget _buildWidgetsTab() {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildWidgetsList(),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: _tabController.index == 0 ? 'Search screens...' : 'Search widgets...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildScreensList() {
    final screensAsync = ref.watch(filteredScreensProvider(_searchQuery));

    return screensAsync.when(
      data: (screens) {
        if (screens.isEmpty) {
          return _buildEmptyState(
            'No screens found',
            'Create your first custom screen to get started.',
            Icons.web,
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(customScreensProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: screens.length,
            itemBuilder: (context, index) {
              final screen = screens[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: ScreenCard(
                  screen: screen,
                  onEdit: () => _editScreen(screen),
                  onDelete: () => _deleteScreen(screen),
                  onToggleActive: () => _toggleScreenActive(screen),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error loading screens',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(customScreensProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWidgetsList() {
    final widgetsAsync = ref.watch(filteredWidgetsProvider(_searchQuery));

    return widgetsAsync.when(
      data: (widgets) {
        if (widgets.isEmpty) {
          return _buildEmptyState(
            'No widgets found',
            'Create your first custom widget to get started.',
            Icons.widgets,
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(customWidgetsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: widgets.length,
            itemBuilder: (context, index) {
              final widget = widgets[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: WidgetCard(
                  widget: widget,
                  onEdit: () => _editWidget(widget),
                  onDelete: () => _deleteWidget(widget),
                  onToggleVisible: () => _toggleWidgetVisible(widget),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error loading widgets',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(customWidgetsProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () => _showCreateDialog(),
            icon: const Icon(Icons.add),
            label: Text(_tabController.index == 0 ? 'Create Screen' : 'Create Widget'),
          ),
        ],
      ),
    );
  }

  void _showCreateDialog() {
    if (_tabController.index == 0) {
      _showCreateScreenDialog();
    } else {
      _showCreateWidgetDialog();
    }
  }

  void _showCreateScreenDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateScreenDialog(),
    );
  }

  void _showCreateWidgetDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateWidgetDialog(),
    );
  }

  void _editScreen(CustomScreen screen) {
    showDialog(
      context: context,
      builder: (context) => CreateScreenDialog(screen: screen),
    );
  }

  void _editWidget(CustomWidget widget) {
    showDialog(
      context: context,
      builder: (context) => CreateWidgetDialog(widget: widget),
    );
  }

  void _deleteScreen(CustomScreen screen) async {
    final confirmed = await AppUtils.showConfirmDialog(
      context,
      title: 'Delete Screen',
      message: 'Are you sure you want to delete "${screen.title}"? This action cannot be undone.',
    );

    if (confirmed == true) {
      final success = await ref.read(screenManagementProvider.notifier).deleteScreen(screen.id);
      if (success && mounted) {
        AppUtils.showSuccessSnackBar(context, 'Screen deleted successfully');
      }
    }
  }

  void _deleteWidget(CustomWidget widget) async {
    final confirmed = await AppUtils.showConfirmDialog(
      context,
      title: 'Delete Widget',
      message: 'Are you sure you want to delete "${widget.title}"? This action cannot be undone.',
    );

    if (confirmed == true) {
      final success = await ref.read(widgetManagementProvider.notifier).deleteWidget(widget.id);
      if (success && mounted) {
        AppUtils.showSuccessSnackBar(context, 'Widget deleted successfully');
      }
    }
  }

  void _toggleScreenActive(CustomScreen screen) async {
    final updatedData = {
      'isActive': !screen.isActive,
    };

    final success = await ref.read(screenManagementProvider.notifier).updateScreen(screen.id, updatedData);
    if (success && mounted) {
      AppUtils.showSuccessSnackBar(
        context,
        screen.isActive ? 'Screen deactivated' : 'Screen activated'
      );
    }
  }

  void _toggleWidgetVisible(CustomWidget widget) async {
    final updatedData = {
      'isVisible': !widget.isVisible,
    };

    final success = await ref.read(widgetManagementProvider.notifier).updateWidget(widget.id, updatedData);
    if (success && mounted) {
      AppUtils.showSuccessSnackBar(
        context,
        widget.isVisible ? 'Widget hidden' : 'Widget shown'
      );
    }
  }
}
