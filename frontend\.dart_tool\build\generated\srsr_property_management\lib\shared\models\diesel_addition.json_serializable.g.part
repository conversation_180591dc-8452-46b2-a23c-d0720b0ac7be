// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DieselAddition _$DieselAdditionFromJson(Map<String, dynamic> json) =>
    DieselAddition(
      id: json['id'] as String,
      propertyId: json['property_id'] as String,
      quantity: (json['quantity_liters'] as num).toDouble(),
      costPerLiter: (json['cost_per_liter'] as num?)?.toDouble(),
      totalCost: (json['total_cost'] as num?)?.toDouble(),
      supplier: json['supplier'] as String?,
      receiptNumber: json['receipt_number'] as String?,
      notes: json['notes'] as String?,
      addedBy: json['added_by'] as String,
      addedAt: DateTime.parse(json['added_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$DieselAdditionToJson(DieselAddition instance) =>
    <String, dynamic>{
      'id': instance.id,
      'property_id': instance.propertyId,
      'quantity_liters': instance.quantity,
      'cost_per_liter': instance.costPerLiter,
      'total_cost': instance.totalCost,
      'supplier': instance.supplier,
      'receipt_number': instance.receiptNumber,
      'notes': instance.notes,
      'added_by': instance.addedBy,
      'added_at': instance.addedAt.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
