import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';
import 'property_service.dart';
import '../../core/business_logic/status_calculator.dart';
import '../../core/constants/property_type_constants.dart';

part 'property.g.dart';

@JsonSerializable()
class Property {
  final String id;
  final String name;
  final String type; // residential, office
  final String? address;
  final String? description;
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @JsonKey(name: 'is_active')
  final bool isActive;
  final List<PropertyService>? services;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  const Property({
    required this.id,
    required this.name,
    required this.type,
    this.address,
    this.description,
    this.imageUrl,
    required this.isActive,
    this.services,
    required this.createdAt,
  });

  factory Property.fromJson(Map<String, dynamic> json) => _$PropertyFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyToJson(this);

  Property copyWith({
    String? id,
    String? name,
    String? type,
    String? address,
    String? description,
    String? imageUrl,
    bool? isActive,
    List<PropertyService>? services,
    DateTime? createdAt,
  }) {
    return Property(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      address: address ?? this.address,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      services: services ?? this.services,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Property && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Property(id: $id, name: $name, type: $type, address: $address, description: $description, imageUrl: $imageUrl, isActive: $isActive, services: $services, createdAt: $createdAt)';
  }

  // Business logic methods
  bool get isResidential => type.toLowerCase() == 'residential';
  bool get isOffice => type.toLowerCase() == 'office';
  bool get isConstructionSite => type.toLowerCase() == 'construction_site';

  PropertyType get propertyType => PropertyTypeConstants.fromString(type);
  String get displayType => PropertyTypeConstants.getDisplayName(propertyType);
  String get typeDescription => PropertyTypeConstants.getDescription(propertyType);

  List<PropertyService> get activeServices =>
      services?.where((service) => service.status == 'active').toList() ?? [];

  // Type-specific business logic
  List<String> get defaultServices => PropertyTypeConstants.getDefaultServices(propertyType);
  List<String> get availableFeatures => PropertyTypeConstants.getAvailableFeatures(propertyType);
  List<String> get maintenanceCategories => PropertyTypeConstants.getMaintenanceCategories(propertyType);
  Map<String, dynamic> get defaultThresholds => PropertyTypeConstants.getDefaultThresholds(propertyType);

  // Service management
  bool hasService(String serviceType) => PropertyTypeConstants.hasService(propertyType, serviceType);
  bool hasFeature(String feature) => PropertyTypeConstants.hasFeature(propertyType, feature);
  int getServicePriority(String serviceType) => PropertyTypeConstants.getServicePriorityValue(propertyType, serviceType);

  // UI helpers
  Color get primaryColor => Color(PropertyTypeConstants.typeColors[propertyType]?['primary'] ?? 0xFF2196F3);
  Color get secondaryColor => Color(PropertyTypeConstants.typeColors[propertyType]?['secondary'] ?? 0xFF42A5F5);
  Color get accentColor => Color(PropertyTypeConstants.typeColors[propertyType]?['accent'] ?? 0xFF2196F3);

  IconData get typeIcon => IconData(
    PropertyTypeConstants.typeIcons[propertyType]?['main'] ?? 0xe318,
    fontFamily: 'MaterialIcons',
  );

  // Service validation
  List<String> get missingDefaultServices {
    final currentServiceTypes = services?.map((s) => s.serviceType.toLowerCase()).toList() ?? [];
    return defaultServices.where((service) => !currentServiceTypes.contains(service)).toList();
  }

  // Status calculations
  double get serviceCompletionPercentage {
    if (defaultServices.isEmpty) return 100.0;
    final currentServiceTypes = services?.map((s) => s.serviceType.toLowerCase()).toList() ?? [];
    final availableServices = defaultServices.where((service) => currentServiceTypes.contains(service)).length;
    return (availableServices / defaultServices.length) * 100;
  }

  bool get hasAllRequiredServices => missingDefaultServices.isEmpty;

  // Critical service status
  List<PropertyService> get criticalServices =>
      services?.where((service) => service.status.toLowerCase() == 'critical').toList() ?? [];

  List<PropertyService> get warningServices =>
      services?.where((service) => service.status.toLowerCase() == 'warning').toList() ?? [];

  bool get hasCriticalIssues => criticalServices.isNotEmpty;
  bool get hasWarnings => warningServices.isNotEmpty;

  // Property health score (0-100)
  double get healthScore {
    if (services == null || services!.isEmpty) return 0.0;

    double score = 0.0;
    int totalWeight = 0;

    for (final service in services!) {
      final priority = getServicePriority(service.serviceType);
      final weight = 11 - priority; // Higher priority = higher weight

      switch (service.status.toLowerCase()) {
        case 'operational':
          score += weight * 100;
          break;
        case 'warning':
          score += weight * 70;
          break;
        case 'critical':
          score += weight * 30;
          break;
        case 'maintenance':
          score += weight * 50;
          break;
      }
      totalWeight += weight;
    }

    return totalWeight > 0 ? score / totalWeight : 0.0;
  }

  String get healthStatus {
    final score = healthScore;
    if (score >= 90) return 'Excellent';
    if (score >= 75) return 'Good';
    if (score >= 60) return 'Fair';
    if (score >= 40) return 'Poor';
    return 'Critical';
  }

  Color get healthColor {
    final score = healthScore;
    if (score >= 90) return Colors.green;
    if (score >= 75) return Colors.lightGreen;
    if (score >= 60) return Colors.orange;
    if (score >= 40) return Colors.deepOrange;
    return Colors.red;
  }

  List<PropertyService> get inactiveServices =>
      services?.where((service) => service.status != 'active').toList() ?? [];

  bool get hasActiveServices => activeServices.isNotEmpty;

  int get serviceCount => services?.length ?? 0;
  int get activeServiceCount => activeServices.length;

  // Status calculation
  StatusLevel calculateOverallStatus() {
    if (!isActive) return StatusLevel.red;
    if (services == null || services!.isEmpty) return StatusLevel.orange;

    final criticalServices = services!.where((s) => s.isCritical).toList();
    final inactiveCriticalServices = criticalServices.where((s) => s.status != 'active').toList();

    if (inactiveCriticalServices.isNotEmpty) return StatusLevel.red;

    final totalServiceCount = serviceCount;

    if (totalServiceCount > 0) {
      final activePercentage = (activeServiceCount / totalServiceCount) * 100;
      if (activePercentage < 80) return StatusLevel.red;
      if (activePercentage < 95) return StatusLevel.orange;
    }

    return StatusLevel.green;
  }

  // Validation methods
  bool get isValid => name.isNotEmpty && type.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    if (name.isEmpty) errors.add('Property name is required');
    if (type.isEmpty) errors.add('Property type is required');
    if (name.length < 2) errors.add('Property name must be at least 2 characters');
    if (name.length > 100) errors.add('Property name cannot exceed 100 characters');
    return errors;
  }

  // Helper methods
  String get formattedCreatedDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  Duration get age => DateTime.now().difference(createdAt);

  String get ageDescription {
    final days = age.inDays;
    if (days < 30) return '$days days old';
    if (days < 365) return '${(days / 30).floor()} months old';
    return '${(days / 365).floor()} years old';
  }

  // Additional getters for compatibility with PropertyDetailsScreen
  IconData get icon => typeIcon;
  String get typeDisplayName => displayType;
  String get createdAtFormatted => formattedCreatedDate;
  String get overallStatusText => healthStatus;
  Color get overallStatusColor => healthColor;
}
