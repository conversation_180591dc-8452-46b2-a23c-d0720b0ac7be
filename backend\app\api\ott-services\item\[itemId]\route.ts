import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const updateOttServiceSchema = Joi.object({
  service_name: Joi.string().min(2).max(100).optional(),
  subscription_type: Joi.string().valid('monthly', 'yearly', 'lifetime').optional(),
  monthly_cost: Joi.number().positive().optional(),
  renewal_date: Joi.date().optional(),
  status: Joi.string().valid('active', 'inactive', 'expired').optional(),
  login_credentials: Joi.string().optional(),
  notes: Joi.string().optional(),
});

async function getOttServiceHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;

    const ottService = await prisma.ottService.findUnique({
      where: { id: itemId },
    });

    if (!ottService) {
      return Response.json(
        createApiResponse(null, 'OTT service not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Transform data to match API response format
    const transformedService = {
      id: ottService.id,
      property_id: ottService.propertyId,
      service_name: ottService.serviceName,
      subscription_type: ottService.subscriptionType,
      monthly_cost: ottService.monthlyCost ? parseFloat(ottService.monthlyCost.toString()) : null,
      renewal_date: ottService.renewalDate,
      status: ottService.status.toLowerCase(),
      login_credentials: ottService.loginCredentials,
      notes: ottService.notes,
      created_at: ottService.createdAt,
      updated_at: ottService.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedService),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch OTT service');
  }
}

async function updateOttServiceHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateOttServiceSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      service_name, 
      subscription_type, 
      monthly_cost, 
      renewal_date, 
      status, 
      login_credentials, 
      notes 
    } = validation.data;

    // Check if OTT service exists
    const existingService = await prisma.ottService.findUnique({
      where: { id: itemId },
    });

    if (!existingService) {
      return Response.json(
        createApiResponse(null, 'OTT service not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Update OTT service
    const updatedService = await prisma.ottService.update({
      where: { id: itemId },
      data: {
        ...(service_name && { serviceName: service_name }),
        ...(subscription_type && { subscriptionType: subscription_type }),
        ...(monthly_cost !== undefined && { monthlyCost: monthly_cost }),
        ...(renewal_date && { renewalDate: new Date(renewal_date) }),
        ...(status && { status: status.toUpperCase() }),
        ...(login_credentials !== undefined && { loginCredentials: login_credentials }),
        ...(notes !== undefined && { notes }),
      },
    });

    // Transform data to match API response format
    const transformedService = {
      id: updatedService.id,
      property_id: updatedService.propertyId,
      service_name: updatedService.serviceName,
      subscription_type: updatedService.subscriptionType,
      monthly_cost: updatedService.monthlyCost ? parseFloat(updatedService.monthlyCost.toString()) : null,
      renewal_date: updatedService.renewalDate,
      status: updatedService.status.toLowerCase(),
      login_credentials: updatedService.loginCredentials,
      notes: updatedService.notes,
      created_at: updatedService.createdAt,
      updated_at: updatedService.updatedAt,
    };

    return Response.json(
      createApiResponse({
        message: 'OTT service updated successfully',
        service: transformedService,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update OTT service');
  }
}

async function deleteOttServiceHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;

    // Check if OTT service exists
    const existingService = await prisma.ottService.findUnique({
      where: { id: itemId },
    });

    if (!existingService) {
      return Response.json(
        createApiResponse(null, 'OTT service not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Delete OTT service
    await prisma.ottService.delete({
      where: { id: itemId },
    });

    return Response.json(
      createApiResponse({
        message: 'OTT service deleted successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete OTT service');
  }
}

export const GET = requireAuth(getOttServiceHandler);
export const PUT = requireAuth(updateOttServiceHandler);
export const DELETE = requireAuth(deleteOttServiceHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
