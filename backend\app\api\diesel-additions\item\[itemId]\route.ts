import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const updateDieselAdditionSchema = Joi.object({
  quantity_liters: Joi.number().positive().optional(),
  cost_per_liter: Joi.number().positive().optional(),
  total_cost: Joi.number().positive().optional(),
  supplier: Joi.string().min(2).max(100).optional(),
  invoice_number: Joi.string().max(50).optional(),
  delivery_date: Joi.date().optional(),
  quality_rating: Joi.number().min(1).max(5).optional(),
  notes: Joi.string().optional(),
});

async function getDieselAdditionHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;

    const dieselAddition = await prisma.dieselAddition.findUnique({
      where: { id: itemId },
    });

    if (!dieselAddition) {
      return Response.json(
        createApiResponse(null, 'Diesel addition not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Transform data to match API response format
    const transformedAddition = {
      id: dieselAddition.id,
      property_id: dieselAddition.propertyId,
      quantity_liters: dieselAddition.quantityLiters ? parseFloat(dieselAddition.quantityLiters.toString()) : null,
      cost_per_liter: dieselAddition.costPerLiter ? parseFloat(dieselAddition.costPerLiter.toString()) : null,
      total_cost: dieselAddition.totalCost ? parseFloat(dieselAddition.totalCost.toString()) : null,
      supplier: dieselAddition.supplier,
      invoice_number: dieselAddition.invoiceNumber,
      delivery_date: dieselAddition.deliveryDate,
      quality_rating: dieselAddition.qualityRating,
      notes: dieselAddition.notes,
      created_at: dieselAddition.createdAt,
      updated_at: dieselAddition.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedAddition),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch diesel addition');
  }
}

async function updateDieselAdditionHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateDieselAdditionSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      quantity_liters, 
      cost_per_liter, 
      total_cost, 
      supplier, 
      invoice_number, 
      delivery_date, 
      quality_rating, 
      notes 
    } = validation.data;

    // Check if diesel addition exists
    const existingAddition = await prisma.dieselAddition.findUnique({
      where: { id: itemId },
    });

    if (!existingAddition) {
      return Response.json(
        createApiResponse(null, 'Diesel addition not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Update diesel addition
    const updatedAddition = await prisma.dieselAddition.update({
      where: { id: itemId },
      data: {
        ...(quantity_liters !== undefined && { quantityLiters: quantity_liters }),
        ...(cost_per_liter !== undefined && { costPerLiter: cost_per_liter }),
        ...(total_cost !== undefined && { totalCost: total_cost }),
        ...(supplier && { supplier }),
        ...(invoice_number !== undefined && { invoiceNumber: invoice_number }),
        ...(delivery_date && { deliveryDate: new Date(delivery_date) }),
        ...(quality_rating !== undefined && { qualityRating: quality_rating }),
        ...(notes !== undefined && { notes }),
      },
    });

    // Transform data to match API response format
    const transformedAddition = {
      id: updatedAddition.id,
      property_id: updatedAddition.propertyId,
      quantity_liters: updatedAddition.quantityLiters ? parseFloat(updatedAddition.quantityLiters.toString()) : null,
      cost_per_liter: updatedAddition.costPerLiter ? parseFloat(updatedAddition.costPerLiter.toString()) : null,
      total_cost: updatedAddition.totalCost ? parseFloat(updatedAddition.totalCost.toString()) : null,
      supplier: updatedAddition.supplier,
      invoice_number: updatedAddition.invoiceNumber,
      delivery_date: updatedAddition.deliveryDate,
      quality_rating: updatedAddition.qualityRating,
      notes: updatedAddition.notes,
      created_at: updatedAddition.createdAt,
      updated_at: updatedAddition.updatedAt,
    };

    return Response.json(
      createApiResponse({
        message: 'Diesel addition updated successfully',
        addition: transformedAddition,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update diesel addition');
  }
}

async function deleteDieselAdditionHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;

    // Check if diesel addition exists
    const existingAddition = await prisma.dieselAddition.findUnique({
      where: { id: itemId },
    });

    if (!existingAddition) {
      return Response.json(
        createApiResponse(null, 'Diesel addition not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Delete diesel addition
    await prisma.dieselAddition.delete({
      where: { id: itemId },
    });

    return Response.json(
      createApiResponse({
        message: 'Diesel addition deleted successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete diesel addition');
  }
}

export const GET = requireAuth(getDieselAdditionHandler);
export const PUT = requireAuth(updateDieselAdditionHandler);
export const DELETE = requireAuth(deleteDieselAdditionHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
