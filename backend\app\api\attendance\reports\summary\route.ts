import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getQueryParams, handleError, corsHeaders } from '@/lib/utils';

async function getAttendanceSummaryHandler(
  request: NextRequest, 
  context: any, 
  currentUser: any
) {
  try {
    const params = getQueryParams(request);
    const { start_date, end_date, property_id } = params;

    // Build date range filter
    const dateFilter: any = {};
    if (start_date && end_date) {
      dateFilter.date = {
        gte: new Date(start_date),
        lte: new Date(end_date),
      };
    } else if (start_date) {
      dateFilter.date = {
        gte: new Date(start_date),
      };
    } else if (end_date) {
      dateFilter.date = {
        lte: new Date(end_date),
      };
    }

    // Build property filter
    const propertyFilter: any = {};
    if (property_id) {
      propertyFilter.propertyId = property_id;
    }

    // Combine filters
    const where = { ...dateFilter, ...propertyFilter };

    // Get attendance records
    const attendanceRecords = await prisma.attendance.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });

    // Calculate summary statistics
    const totalRecords = attendanceRecords.length;
    const uniqueUsers = new Set(attendanceRecords.map(r => r.userId)).size;
    const uniqueProperties = new Set(attendanceRecords.map(r => r.propertyId)).size;

    // Calculate status breakdown
    const statusBreakdown = attendanceRecords.reduce((acc, record) => {
      const status = record.status.toLowerCase();
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Calculate average hours worked
    const totalHours = attendanceRecords.reduce((sum, record) => {
      if (record.hoursWorked) {
        return sum + parseFloat(record.hoursWorked.toString());
      }
      return sum;
    }, 0);
    const averageHours = totalRecords > 0 ? totalHours / totalRecords : 0;

    // Group by property
    const byProperty = attendanceRecords.reduce((acc, record) => {
      const propertyId = record.propertyId;
      if (!acc[propertyId]) {
        acc[propertyId] = {
          property_id: propertyId,
          property_name: record.property.name,
          property_type: record.property.type,
          total_records: 0,
          unique_users: new Set(),
          status_breakdown: {},
          total_hours: 0,
        };
      }
      
      acc[propertyId].total_records++;
      acc[propertyId].unique_users.add(record.userId);
      
      const status = record.status.toLowerCase();
      acc[propertyId].status_breakdown[status] = (acc[propertyId].status_breakdown[status] || 0) + 1;
      
      if (record.hoursWorked) {
        acc[propertyId].total_hours += parseFloat(record.hoursWorked.toString());
      }
      
      return acc;
    }, {} as Record<string, any>);

    // Convert property data to array and clean up
    const propertyStats = Object.values(byProperty).map((prop: any) => ({
      ...prop,
      unique_users: prop.unique_users.size,
      average_hours: prop.total_records > 0 ? prop.total_hours / prop.total_records : 0,
    }));

    // Group by user
    const byUser = attendanceRecords.reduce((acc, record) => {
      const userId = record.userId;
      if (!acc[userId]) {
        acc[userId] = {
          user_id: userId,
          user_name: record.user.fullName,
          user_email: record.user.email,
          total_records: 0,
          status_breakdown: {},
          total_hours: 0,
          properties: new Set(),
        };
      }
      
      acc[userId].total_records++;
      acc[userId].properties.add(record.propertyId);
      
      const status = record.status.toLowerCase();
      acc[userId].status_breakdown[status] = (acc[userId].status_breakdown[status] || 0) + 1;
      
      if (record.hoursWorked) {
        acc[userId].total_hours += parseFloat(record.hoursWorked.toString());
      }
      
      return acc;
    }, {} as Record<string, any>);

    // Convert user data to array and clean up
    const userStats = Object.values(byUser).map((user: any) => ({
      ...user,
      properties_count: user.properties.size,
      average_hours: user.total_records > 0 ? user.total_hours / user.total_records : 0,
      properties: undefined, // Remove Set object
    }));

    const summary = {
      period: {
        start_date: start_date || null,
        end_date: end_date || null,
        property_id: property_id || null,
      },
      totals: {
        total_records: totalRecords,
        unique_users: uniqueUsers,
        unique_properties: uniqueProperties,
        total_hours: totalHours,
        average_hours: averageHours,
      },
      status_breakdown: statusBreakdown,
      by_property: propertyStats,
      by_user: userStats,
      generated_at: new Date().toISOString(),
    };

    return Response.json(
      createApiResponse(summary),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to generate attendance summary');
  }
}

export const GET = requireAuth(getAttendanceSummaryHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
