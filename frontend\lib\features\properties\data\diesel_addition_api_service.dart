import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../shared/models/api_response.dart';

part 'diesel_addition_api_service.g.dart';

@RestApi()
abstract class DieselAdditionApiService {
  factory DieselAdditionApiService(Dio dio) = _DieselAdditionApiService;

  @GET('/api/diesel-additions/{propertyId}')
  Future<ApiResponse<List<DieselAddition>>> getDieselAdditions(@Path('propertyId') String propertyId);

  @POST('/api/diesel-additions/{propertyId}')
  Future<ApiResponse<DieselAddition>> createDieselAddition(
    @Path('propertyId') String propertyId,
    @Body() CreateDieselAdditionRequest request,
  );

  @GET('/api/diesel-additions/item/{id}')
  Future<ApiResponse<DieselAddition>> getDieselAdditionById(@Path('id') String id);

  @PUT('/api/diesel-additions/item/{id}')
  Future<ApiResponse<DieselAddition>> updateDieselAddition(
    @Path('id') String id,
    @Body() UpdateDieselAdditionRequest request,
  );

  @DELETE('/api/diesel-additions/item/{id}')
  Future<ApiResponse<VoidResponse>> deleteDieselAddition(@Path('id') String id);
}

// Models
class DieselAddition {
  final String id;
  @JsonKey(name: 'property_id')
  final String propertyId;
  @JsonKey(name: 'quantity_liters')
  final double quantityLiters;
  @JsonKey(name: 'cost_per_liter')
  final double costPerLiter;
  @JsonKey(name: 'total_cost')
  final double totalCost;
  @JsonKey(name: 'supplier_name')
  final String? supplierName;
  @JsonKey(name: 'invoice_number')
  final String? invoiceNumber;
  @JsonKey(name: 'delivery_date')
  final DateTime deliveryDate;
  @JsonKey(name: 'fuel_quality')
  final String? fuelQuality;
  final String? notes;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const DieselAddition({
    required this.id,
    required this.propertyId,
    required this.quantityLiters,
    required this.costPerLiter,
    required this.totalCost,
    this.supplierName,
    this.invoiceNumber,
    required this.deliveryDate,
    this.fuelQuality,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DieselAddition.fromJson(Map<String, dynamic> json) => DieselAddition(
        id: json['id'] as String,
        propertyId: json['property_id'] as String,
        quantityLiters: (json['quantity_liters'] as num).toDouble(),
        costPerLiter: (json['cost_per_liter'] as num).toDouble(),
        totalCost: (json['total_cost'] as num).toDouble(),
        supplierName: json['supplier_name'] as String?,
        invoiceNumber: json['invoice_number'] as String?,
        deliveryDate: DateTime.parse(json['delivery_date'] as String),
        fuelQuality: json['fuel_quality'] as String?,
        notes: json['notes'] as String?,
        createdAt: DateTime.parse(json['created_at'] as String),
        updatedAt: DateTime.parse(json['updated_at'] as String),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'property_id': propertyId,
        'quantity_liters': quantityLiters,
        'cost_per_liter': costPerLiter,
        'total_cost': totalCost,
        'supplier_name': supplierName,
        'invoice_number': invoiceNumber,
        'delivery_date': deliveryDate.toIso8601String(),
        'fuel_quality': fuelQuality,
        'notes': notes,
        'created_at': createdAt.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
      };
}

// Request models
class CreateDieselAdditionRequest {
  @JsonKey(name: 'quantity_liters')
  final double quantityLiters;
  @JsonKey(name: 'cost_per_liter')
  final double costPerLiter;
  @JsonKey(name: 'supplier_name')
  final String? supplierName;
  @JsonKey(name: 'invoice_number')
  final String? invoiceNumber;
  @JsonKey(name: 'delivery_date')
  final DateTime deliveryDate;
  @JsonKey(name: 'fuel_quality')
  final String? fuelQuality;
  final String? notes;

  const CreateDieselAdditionRequest({
    required this.quantityLiters,
    required this.costPerLiter,
    this.supplierName,
    this.invoiceNumber,
    required this.deliveryDate,
    this.fuelQuality,
    this.notes,
  });

  Map<String, dynamic> toJson() => {
        'quantity_liters': quantityLiters,
        'cost_per_liter': costPerLiter,
        if (supplierName != null) 'supplier_name': supplierName,
        if (invoiceNumber != null) 'invoice_number': invoiceNumber,
        'delivery_date': deliveryDate.toIso8601String(),
        if (fuelQuality != null) 'fuel_quality': fuelQuality,
        if (notes != null) 'notes': notes,
      };
}

class UpdateDieselAdditionRequest {
  @JsonKey(name: 'quantity_liters')
  final double? quantityLiters;
  @JsonKey(name: 'cost_per_liter')
  final double? costPerLiter;
  @JsonKey(name: 'supplier_name')
  final String? supplierName;
  @JsonKey(name: 'invoice_number')
  final String? invoiceNumber;
  @JsonKey(name: 'delivery_date')
  final DateTime? deliveryDate;
  @JsonKey(name: 'fuel_quality')
  final String? fuelQuality;
  final String? notes;

  const UpdateDieselAdditionRequest({
    this.quantityLiters,
    this.costPerLiter,
    this.supplierName,
    this.invoiceNumber,
    this.deliveryDate,
    this.fuelQuality,
    this.notes,
  });

  Map<String, dynamic> toJson() => {
        if (quantityLiters != null) 'quantity_liters': quantityLiters,
        if (costPerLiter != null) 'cost_per_liter': costPerLiter,
        if (supplierName != null) 'supplier_name': supplierName,
        if (invoiceNumber != null) 'invoice_number': invoiceNumber,
        if (deliveryDate != null) 'delivery_date': deliveryDate!.toIso8601String(),
        if (fuelQuality != null) 'fuel_quality': fuelQuality,
        if (notes != null) 'notes': notes,
      };
}
