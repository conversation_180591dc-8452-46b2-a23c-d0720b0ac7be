import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/function_process.dart';
import '../providers/function_processes_providers.dart';
import '../widgets/function_process_card.dart';
import '../widgets/add_function_process_dialog.dart';

class FunctionProcessesScreen extends ConsumerStatefulWidget {
  const FunctionProcessesScreen({super.key});

  @override
  ConsumerState<FunctionProcessesScreen> createState() => _FunctionProcessesScreenState();
}

class _FunctionProcessesScreenState extends ConsumerState<FunctionProcessesScreen> {
  String _selectedFilter = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final functionProcessesAsync = ref.watch(functionProcessesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Function Processes'),
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['admin.manage'],
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddFunctionProcessDialog(),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(functionProcessesProvider),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search function processes...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip('all', 'All Processes'),
                      const SizedBox(width: 8),
                      _buildFilterChip('active', 'Active'),
                      const SizedBox(width: 8),
                      _buildFilterChip('inactive', 'Inactive'),
                      const SizedBox(width: 8),
                      _buildFilterChip('maintenance', 'Maintenance'),
                      const SizedBox(width: 8),
                      _buildFilterChip('automated', 'Automated'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Processes List
          Expanded(
            child: functionProcessesAsync.when(
              data: (processes) {
                final filteredProcesses = _filterProcesses(processes);

                if (filteredProcesses.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(functionProcessesProvider);
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    itemCount: filteredProcesses.length,
                    itemBuilder: (context, index) {
                      final process = filteredProcesses[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                        child: FunctionProcessCard(
                          process: process,
                          onTap: () => _showProcessDetails(process),
                          onEdit: () => _showEditProcessDialog(process),
                          onDelete: () => _showDeleteConfirmation(process),
                          onToggleStatus: () => _toggleProcessStatus(process),
                        ),
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
        ],
      ),
      floatingActionButton: RoleBasedWidget(
        requiredPermissions: const ['admin.manage'],
        child: FloatingActionButton(
          onPressed: () => _showAddFunctionProcessDialog(),
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  List<FunctionProcess> _filterProcesses(List<FunctionProcess> processes) {
    var filtered = processes;

    // Apply status filter
    if (_selectedFilter != 'all') {
      filtered = filtered.where((process) {
        switch (_selectedFilter) {
          case 'active':
            return process.isActive;
          case 'inactive':
            return !process.isActive;
          case 'maintenance':
            return process.status.toLowerCase() == 'maintenance';
          case 'automated':
            return process.isAutomated;
          default:
            return true;
        }
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((process) {
        return process.name.toLowerCase().contains(query) ||
               (process.description?.toLowerCase().contains(query) ?? false) ||
               process.category.toLowerCase().contains(query);
      }).toList();
    }

    // Sort by priority and name
    filtered.sort((a, b) {
      final priorityComparison = a.priorityValue.compareTo(b.priorityValue);
      if (priorityComparison != 0) return priorityComparison;
      return a.name.compareTo(b.name);
    });

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.settings_applications,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Function Processes Found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != 'all'
                ? 'Try adjusting your search or filters'
                : 'Add your first function process to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          RoleBasedWidget(
            requiredPermissions: const ['admin.manage'],
            child: ElevatedButton.icon(
              onPressed: () => _showAddFunctionProcessDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add Function Process'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load function processes',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(functionProcessesProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showAddFunctionProcessDialog() {
    showDialog(
      context: context,
      builder: (context) => AddFunctionProcessDialog(
        onProcessAdded: () {
          ref.invalidate(functionProcessesProvider);
          AppUtils.showSuccessSnackBar(context, 'Function process added successfully');
        },
      ),
    );
  }

  void _showProcessDetails(FunctionProcess process) {
    // TODO: Navigate to process details screen
    AppUtils.showInfoSnackBar(context, 'Process details: ${process.name}');
  }

  void _showEditProcessDialog(FunctionProcess process) {
    showDialog(
      context: context,
      builder: (context) => AddFunctionProcessDialog(
        process: process,
        onProcessAdded: () {
          ref.invalidate(functionProcessesProvider);
          AppUtils.showSuccessSnackBar(context, 'Function process updated successfully');
        },
      ),
    );
  }

  void _showDeleteConfirmation(FunctionProcess process) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Function Process'),
        content: Text('Are you sure you want to delete "${process.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(functionProcessesNotifierProvider.notifier)
                    .deleteFunctionProcess(process.id);
                AppUtils.showSuccessSnackBar(context, 'Function process deleted successfully');
              } catch (e) {
                AppUtils.showErrorSnackBar(context, 'Failed to delete function process: $e');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _toggleProcessStatus(FunctionProcess process) async {
    try {
      await ref.read(functionProcessesNotifierProvider.notifier)
          .toggleProcessStatus(process.id);
      AppUtils.showSuccessSnackBar(
        context,
        'Process ${process.isActive ? 'deactivated' : 'activated'} successfully'
      );
    } catch (e) {
      AppUtils.showErrorSnackBar(context, 'Failed to update process status: $e');
    }
  }
}
