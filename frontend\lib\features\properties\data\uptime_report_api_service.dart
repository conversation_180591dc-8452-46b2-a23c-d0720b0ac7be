import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../shared/models/api_response.dart';

part 'uptime_report_api_service.g.dart';

@RestApi()
abstract class UptimeReportApiService {
  factory UptimeReportApiService(Dio dio) = _UptimeReportApiService;

  @GET('/api/uptime-reports/{propertyId}')
  Future<ApiResponse<List<UptimeReport>>> getUptimeReports(@Path('propertyId') String propertyId);

  @POST('/api/uptime-reports/{propertyId}')
  Future<ApiResponse<UptimeReport>> createUptimeReport(
    @Path('propertyId') String propertyId,
    @Body() CreateUptimeReportRequest request,
  );

  @GET('/api/uptime-reports/{id}')
  Future<ApiResponse<UptimeReport>> getUptimeReportById(@Path('id') String id);

  @PUT('/api/uptime-reports/{id}')
  Future<ApiResponse<UptimeReport>> updateUptimeReport(
    @Path('id') String id,
    @Body() UpdateUptimeReportRequest request,
  );

  @DELETE('/api/uptime-reports/{id}')
  Future<ApiResponse<VoidResponse>> deleteUptimeReport(@Path('id') String id);
}

// Models
class UptimeReport {
  final String id;
  @JsonKey(name: 'property_id')
  final String propertyId;
  @JsonKey(name: 'service_type')
  final String serviceType;
  final String date;
  @JsonKey(name: 'uptime_percentage')
  final double uptimePercentage;
  @JsonKey(name: 'downtime_minutes')
  final int downtimeMinutes;
  @JsonKey(name: 'incidents_count')
  final int incidentsCount;
  final String? notes;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const UptimeReport({
    required this.id,
    required this.propertyId,
    required this.serviceType,
    required this.date,
    required this.uptimePercentage,
    required this.downtimeMinutes,
    required this.incidentsCount,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UptimeReport.fromJson(Map<String, dynamic> json) => UptimeReport(
        id: json['id'] as String,
        propertyId: json['property_id'] as String,
        serviceType: json['service_type'] as String,
        date: json['date'] as String,
        uptimePercentage: (json['uptime_percentage'] as num).toDouble(),
        downtimeMinutes: json['downtime_minutes'] as int,
        incidentsCount: json['incidents_count'] as int,
        notes: json['notes'] as String?,
        createdAt: DateTime.parse(json['created_at'] as String),
        updatedAt: DateTime.parse(json['updated_at'] as String),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'property_id': propertyId,
        'service_type': serviceType,
        'date': date,
        'uptime_percentage': uptimePercentage,
        'downtime_minutes': downtimeMinutes,
        'incidents_count': incidentsCount,
        'notes': notes,
        'created_at': createdAt.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
      };
}

// Request models
class CreateUptimeReportRequest {
  @JsonKey(name: 'service_type')
  final String serviceType;
  final String date;
  @JsonKey(name: 'uptime_percentage')
  final double uptimePercentage;
  @JsonKey(name: 'downtime_minutes')
  final int downtimeMinutes;
  @JsonKey(name: 'incidents_count')
  final int incidentsCount;
  final String? notes;

  const CreateUptimeReportRequest({
    required this.serviceType,
    required this.date,
    required this.uptimePercentage,
    required this.downtimeMinutes,
    required this.incidentsCount,
    this.notes,
  });

  Map<String, dynamic> toJson() => {
        'service_type': serviceType,
        'date': date,
        'uptime_percentage': uptimePercentage,
        'downtime_minutes': downtimeMinutes,
        'incidents_count': incidentsCount,
        if (notes != null) 'notes': notes,
      };
}

class UpdateUptimeReportRequest {
  @JsonKey(name: 'service_type')
  final String? serviceType;
  final String? date;
  @JsonKey(name: 'uptime_percentage')
  final double? uptimePercentage;
  @JsonKey(name: 'downtime_minutes')
  final int? downtimeMinutes;
  @JsonKey(name: 'incidents_count')
  final int? incidentsCount;
  final String? notes;

  const UpdateUptimeReportRequest({
    this.serviceType,
    this.date,
    this.uptimePercentage,
    this.downtimeMinutes,
    this.incidentsCount,
    this.notes,
  });

  Map<String, dynamic> toJson() => {
        if (serviceType != null) 'service_type': serviceType,
        if (date != null) 'date': date,
        if (uptimePercentage != null) 'uptime_percentage': uptimePercentage,
        if (downtimeMinutes != null) 'downtime_minutes': downtimeMinutes,
        if (incidentsCount != null) 'incidents_count': incidentsCount,
        if (notes != null) 'notes': notes,
      };
}
