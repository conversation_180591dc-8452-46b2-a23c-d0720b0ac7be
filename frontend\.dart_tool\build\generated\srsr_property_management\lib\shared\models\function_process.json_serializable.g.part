// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunctionProcess _$FunctionProcessFromJson(Map<String, dynamic> json) =>
    FunctionProcess(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      category: json['category'] as String,
      priority: json['priority'] as String,
      status: json['status'] as String,
      isAutomated: json['is_automated'] as bool,
      executionFrequency: json['execution_frequency'] as String?,
      lastExecuted: json['last_executed'] == null
          ? null
          : DateTime.parse(json['last_executed'] as String),
      nextExecution: json['next_execution'] == null
          ? null
          : DateTime.parse(json['next_execution'] as String),
      successRate: (json['success_rate'] as num?)?.toDouble(),
      averageDuration: (json['average_duration'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$FunctionProcessToJson(FunctionProcess instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': instance.category,
      'priority': instance.priority,
      'status': instance.status,
      'is_automated': instance.isAutomated,
      'execution_frequency': instance.executionFrequency,
      'last_executed': instance.lastExecuted?.toIso8601String(),
      'next_execution': instance.nextExecution?.toIso8601String(),
      'success_rate': instance.successRate,
      'average_duration': instance.averageDuration,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
