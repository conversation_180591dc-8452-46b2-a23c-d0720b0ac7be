import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/diesel_addition.dart' as models;
import '../../data/diesel_addition_api_service.dart' as api;
import '../../../../core/network/dio_client.dart';

// API Service Provider
final dieselAdditionApiProvider = Provider<api.DieselAdditionApiService>((ref) {
  final dio = ref.read(dioClientProvider);
  return api.DieselAdditionApiService(dio);
});

// Repository Provider
final dieselAdditionRepositoryProvider = Provider<DieselAdditionRepository>((ref) {
  final apiService = ref.read(dieselAdditionApiProvider);
  return DieselAdditionRepositoryImpl(apiService);
});

// Diesel Additions by Property Provider
final dieselAdditionsByPropertyProvider = FutureProvider.family<List<models.DieselAddition>, String>((ref, propertyId) async {
  final repository = ref.read(dieselAdditionRepositoryProvider);
  return repository.getDieselAdditions(propertyId);
});

// Diesel Additions Notifier for CRUD operations
final dieselAdditionsNotifierProvider = StateNotifierProvider.family<DieselAdditionsNotifier, DieselAdditionsState, String>(
  (ref, propertyId) {
    final repository = ref.read(dieselAdditionRepositoryProvider);
    return DieselAdditionsNotifier(repository, propertyId);
  },
);

// State class for Diesel Additions
class DieselAdditionsState {
  final List<models.DieselAddition> additions;
  final bool isLoading;
  final String? error;

  const DieselAdditionsState({
    this.additions = const [],
    this.isLoading = false,
    this.error,
  });

  DieselAdditionsState copyWith({
    List<models.DieselAddition>? additions,
    bool? isLoading,
    String? error,
  }) {
    return DieselAdditionsState(
      additions: additions ?? this.additions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// State Notifier for Diesel Additions CRUD operations
class DieselAdditionsNotifier extends StateNotifier<DieselAdditionsState> {
  final DieselAdditionRepository _repository;
  final String _propertyId;

  DieselAdditionsNotifier(this._repository, this._propertyId) : super(const DieselAdditionsState()) {
    loadAdditions();
  }

  Future<void> loadAdditions() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final additions = await _repository.getDieselAdditions(_propertyId);
      state = state.copyWith(additions: additions, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> addDieselAddition(api.CreateDieselAdditionRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final newAddition = await _repository.createDieselAddition(_propertyId, request);
      final updatedAdditions = [...state.additions, newAddition];
      state = state.copyWith(additions: updatedAdditions, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> updateDieselAddition(String additionId, api.UpdateDieselAdditionRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedAddition = await _repository.updateDieselAddition(additionId, request);
      final updatedAdditions = state.additions.map((addition) {
        return addition.id == additionId ? updatedAddition : addition;
      }).toList();
      state = state.copyWith(additions: updatedAdditions, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> deleteDieselAddition(String additionId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _repository.deleteDieselAddition(additionId);
      final updatedAdditions = state.additions.where((addition) => addition.id != additionId).toList();
      state = state.copyWith(additions: updatedAdditions, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Repository Interface
abstract class DieselAdditionRepository {
  Future<List<models.DieselAddition>> getDieselAdditions(String propertyId);
  Future<models.DieselAddition> createDieselAddition(String propertyId, api.CreateDieselAdditionRequest request);
  Future<models.DieselAddition> updateDieselAddition(String additionId, api.UpdateDieselAdditionRequest request);
  Future<void> deleteDieselAddition(String additionId);
}

// Repository Implementation
class DieselAdditionRepositoryImpl implements DieselAdditionRepository {
  final api.DieselAdditionApiService _apiService;

  DieselAdditionRepositoryImpl(this._apiService);

  @override
  Future<List<models.DieselAddition>> getDieselAdditions(String propertyId) async {
    try {
      final response = await _apiService.getDieselAdditions(propertyId);
      if (response.success && response.data != null) {
        // Convert API models to domain models
        return response.data!.map((apiModel) => models.DieselAddition(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          quantity: apiModel.quantityLiters,
          costPerLiter: apiModel.costPerLiter,
          totalCost: apiModel.totalCost,
          supplier: apiModel.supplierName,
          receiptNumber: apiModel.invoiceNumber,
          notes: apiModel.notes,
          addedBy: 'system', // TODO: Get from API
          addedAt: apiModel.deliveryDate,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        )).toList();
      } else {
        throw Exception(response.message ?? 'Failed to fetch diesel additions');
      }
    } catch (e) {
      throw Exception('Failed to fetch diesel additions: $e');
    }
  }

  @override
  Future<models.DieselAddition> createDieselAddition(String propertyId, api.CreateDieselAdditionRequest request) async {
    try {
      final response = await _apiService.createDieselAddition(propertyId, request);
      if (response.success && response.data != null) {
        final apiModel = response.data!;
        return models.DieselAddition(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          quantity: apiModel.quantityLiters,
          costPerLiter: apiModel.costPerLiter,
          totalCost: apiModel.totalCost,
          supplier: apiModel.supplierName,
          receiptNumber: apiModel.invoiceNumber,
          notes: apiModel.notes,
          addedBy: 'system', // TODO: Get from API
          addedAt: apiModel.deliveryDate,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        );
      } else {
        throw Exception(response.message ?? 'Failed to create diesel addition');
      }
    } catch (e) {
      throw Exception('Failed to create diesel addition: $e');
    }
  }

  @override
  Future<models.DieselAddition> updateDieselAddition(String additionId, api.UpdateDieselAdditionRequest request) async {
    try {
      final response = await _apiService.updateDieselAddition(additionId, request);
      if (response.success && response.data != null) {
        final apiModel = response.data!;
        return models.DieselAddition(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          quantity: apiModel.quantityLiters,
          costPerLiter: apiModel.costPerLiter,
          totalCost: apiModel.totalCost,
          supplier: apiModel.supplierName,
          receiptNumber: apiModel.invoiceNumber,
          notes: apiModel.notes,
          addedBy: 'system', // TODO: Get from API
          addedAt: apiModel.deliveryDate,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        );
      } else {
        throw Exception(response.message ?? 'Failed to update diesel addition');
      }
    } catch (e) {
      throw Exception('Failed to update diesel addition: $e');
    }
  }

  @override
  Future<void> deleteDieselAddition(String additionId) async {
    try {
      final response = await _apiService.deleteDieselAddition(additionId);
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to delete diesel addition');
      }
    } catch (e) {
      throw Exception('Failed to delete diesel addition: $e');
    }
  }
}
