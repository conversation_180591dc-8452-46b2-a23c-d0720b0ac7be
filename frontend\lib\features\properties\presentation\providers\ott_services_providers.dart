import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/ott_service.dart' as models;
import '../../data/ott_service_api_service.dart' as api;
import '../../../../core/network/dio_client.dart';

// API Service Provider
final ottServiceApiProvider = Provider<api.OttServiceApiService>((ref) {
  final dio = ref.read(dioClientProvider);
  return api.OttServiceApiService(dio);
});

// Repository Provider
final ottServiceRepositoryProvider = Provider<OttServiceRepository>((ref) {
  final apiService = ref.read(ottServiceApiProvider);
  return OttServiceRepositoryImpl(apiService);
});

// OTT Services by Property Provider
final ottServicesByPropertyProvider = FutureProvider.family<List<models.OttService>, String>((ref, propertyId) async {
  final repository = ref.read(ottServiceRepositoryProvider);
  return repository.getOttServices(propertyId);
});

// OTT Services Notifier for CRUD operations
final ottServicesNotifierProvider = StateNotifierProvider.family<OttServicesNotifier, OttServicesState, String>(
  (ref, propertyId) {
    final repository = ref.read(ottServiceRepositoryProvider);
    return OttServicesNotifier(repository, propertyId);
  },
);

// State class for OTT Services
class OttServicesState {
  final List<models.OttService> services;
  final bool isLoading;
  final String? error;

  const OttServicesState({
    this.services = const [],
    this.isLoading = false,
    this.error,
  });

  OttServicesState copyWith({
    List<models.OttService>? services,
    bool? isLoading,
    String? error,
  }) {
    return OttServicesState(
      services: services ?? this.services,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// State Notifier for OTT Services CRUD operations
class OttServicesNotifier extends StateNotifier<OttServicesState> {
  final OttServiceRepository _repository;
  final String _propertyId;

  OttServicesNotifier(this._repository, this._propertyId) : super(const OttServicesState()) {
    loadServices();
  }

  Future<void> loadServices() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final services = await _repository.getOttServices(_propertyId);
      state = state.copyWith(services: services, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> addOttService(api.CreateOttServiceRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final newService = await _repository.createOttService(_propertyId, request);
      final updatedServices = [...state.services, newService];
      state = state.copyWith(services: updatedServices, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> updateOttService(String serviceId, api.UpdateOttServiceRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedService = await _repository.updateOttService(serviceId, request);
      final updatedServices = state.services.map((service) {
        return service.id == serviceId ? updatedService : service;
      }).toList();
      state = state.copyWith(services: updatedServices, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> deleteOttService(String serviceId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _repository.deleteOttService(serviceId);
      final updatedServices = state.services.where((service) => service.id != serviceId).toList();
      state = state.copyWith(services: updatedServices, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Repository Interface
abstract class OttServiceRepository {
  Future<List<models.OttService>> getOttServices(String propertyId);
  Future<models.OttService> createOttService(String propertyId, api.CreateOttServiceRequest request);
  Future<models.OttService> updateOttService(String serviceId, api.UpdateOttServiceRequest request);
  Future<void> deleteOttService(String serviceId);
}

// Repository Implementation
class OttServiceRepositoryImpl implements OttServiceRepository {
  final api.OttServiceApiService _apiService;

  OttServiceRepositoryImpl(this._apiService);

  @override
  Future<List<models.OttService>> getOttServices(String propertyId) async {
    try {
      final response = await _apiService.getOttServices(propertyId);
      if (response.success && response.data != null) {
        // Convert API models to domain models
        return response.data!.map((apiModel) => models.OttService(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          serviceName: apiModel.serviceName,
          provider: apiModel.serviceName, // Use serviceName as provider for now
          description: apiModel.notes,
          monthlyCost: apiModel.monthlyCost,
          subscriptionType: apiModel.subscriptionType,
          subscriptionStartDate: null, // API doesn't have start date
          subscriptionEndDate: apiModel.renewalDate,
          status: apiModel.status,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        )).toList();
      } else {
        throw Exception(response.message ?? 'Failed to fetch OTT services');
      }
    } catch (e) {
      throw Exception('Failed to fetch OTT services: $e');
    }
  }

  @override
  Future<models.OttService> createOttService(String propertyId, api.CreateOttServiceRequest request) async {
    try {
      final response = await _apiService.createOttService(propertyId, request);
      if (response.success && response.data != null) {
        final apiModel = response.data!;
        return models.OttService(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          serviceName: apiModel.serviceName,
          provider: apiModel.serviceName, // Use serviceName as provider for now
          description: apiModel.notes,
          monthlyCost: apiModel.monthlyCost,
          subscriptionType: apiModel.subscriptionType,
          subscriptionStartDate: null, // API doesn't have start date
          subscriptionEndDate: apiModel.renewalDate,
          status: apiModel.status,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        );
      } else {
        throw Exception(response.message ?? 'Failed to create OTT service');
      }
    } catch (e) {
      throw Exception('Failed to create OTT service: $e');
    }
  }

  @override
  Future<models.OttService> updateOttService(String serviceId, api.UpdateOttServiceRequest request) async {
    try {
      final response = await _apiService.updateOttService(serviceId, request);
      if (response.success && response.data != null) {
        final apiModel = response.data!;
        return models.OttService(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          serviceName: apiModel.serviceName,
          provider: apiModel.serviceName, // Use serviceName as provider for now
          description: apiModel.notes,
          monthlyCost: apiModel.monthlyCost,
          subscriptionType: apiModel.subscriptionType,
          subscriptionStartDate: null, // API doesn't have start date
          subscriptionEndDate: apiModel.renewalDate,
          status: apiModel.status,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        );
      } else {
        throw Exception(response.message ?? 'Failed to update OTT service');
      }
    } catch (e) {
      throw Exception('Failed to update OTT service: $e');
    }
  }

  @override
  Future<void> deleteOttService(String serviceId) async {
    try {
      final response = await _apiService.deleteOttService(serviceId);
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to delete OTT service');
      }
    } catch (e) {
      throw Exception('Failed to delete OTT service: $e');
    }
  }
}
