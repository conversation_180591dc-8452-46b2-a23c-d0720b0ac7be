import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/ott_service.dart';
import '../../../../shared/models/api_response.dart';
import '../../data/ott_service_api_service.dart';
import '../../../../core/network/dio_client.dart';

// API Service Provider
final ottServiceApiProvider = Provider<OttServiceApiService>((ref) {
  final dio = ref.read(dioClientProvider);
  return OttServiceApiService(dio);
});

// Repository Provider
final ottServiceRepositoryProvider = Provider<OttServiceRepository>((ref) {
  final apiService = ref.read(ottServiceApiProvider);
  return OttServiceRepositoryImpl(apiService);
});

// OTT Services by Property Provider
final ottServicesByPropertyProvider = FutureProvider.family<List<OttService>, String>((ref, propertyId) async {
  final repository = ref.read(ottServiceRepositoryProvider);
  return repository.getOttServices(propertyId);
});

// OTT Services Notifier for CRUD operations
final ottServicesNotifierProvider = StateNotifierProvider.family<OttServicesNotifier, OttServicesState, String>(
  (ref, propertyId) {
    final repository = ref.read(ottServiceRepositoryProvider);
    return OttServicesNotifier(repository, propertyId);
  },
);

// State class for OTT Services
class OttServicesState {
  final List<OttService> services;
  final bool isLoading;
  final String? error;

  const OttServicesState({
    this.services = const [],
    this.isLoading = false,
    this.error,
  });

  OttServicesState copyWith({
    List<OttService>? services,
    bool? isLoading,
    String? error,
  }) {
    return OttServicesState(
      services: services ?? this.services,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// State Notifier for OTT Services CRUD operations
class OttServicesNotifier extends StateNotifier<OttServicesState> {
  final OttServiceRepository _repository;
  final String _propertyId;

  OttServicesNotifier(this._repository, this._propertyId) : super(const OttServicesState()) {
    loadServices();
  }

  Future<void> loadServices() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final services = await _repository.getOttServices(_propertyId);
      state = state.copyWith(services: services, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> addOttService(CreateOttServiceRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final newService = await _repository.createOttService(_propertyId, request);
      final updatedServices = [...state.services, newService];
      state = state.copyWith(services: updatedServices, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> updateOttService(String serviceId, UpdateOttServiceRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedService = await _repository.updateOttService(serviceId, request);
      final updatedServices = state.services.map((service) {
        return service.id == serviceId ? updatedService : service;
      }).toList();
      state = state.copyWith(services: updatedServices, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> deleteOttService(String serviceId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _repository.deleteOttService(serviceId);
      final updatedServices = state.services.where((service) => service.id != serviceId).toList();
      state = state.copyWith(services: updatedServices, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Repository Interface
abstract class OttServiceRepository {
  Future<List<OttService>> getOttServices(String propertyId);
  Future<OttService> createOttService(String propertyId, CreateOttServiceRequest request);
  Future<OttService> updateOttService(String serviceId, UpdateOttServiceRequest request);
  Future<void> deleteOttService(String serviceId);
}

// Repository Implementation
class OttServiceRepositoryImpl implements OttServiceRepository {
  final OttServiceApiService _apiService;

  OttServiceRepositoryImpl(this._apiService);

  @override
  Future<List<OttService>> getOttServices(String propertyId) async {
    try {
      final response = await _apiService.getOttServices(propertyId);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to fetch OTT services');
      }
    } catch (e) {
      throw Exception('Failed to fetch OTT services: $e');
    }
  }

  @override
  Future<OttService> createOttService(String propertyId, CreateOttServiceRequest request) async {
    try {
      final response = await _apiService.createOttService(propertyId, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to create OTT service');
      }
    } catch (e) {
      throw Exception('Failed to create OTT service: $e');
    }
  }

  @override
  Future<OttService> updateOttService(String serviceId, UpdateOttServiceRequest request) async {
    try {
      final response = await _apiService.updateOttService(serviceId, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to update OTT service');
      }
    } catch (e) {
      throw Exception('Failed to update OTT service: $e');
    }
  }

  @override
  Future<void> deleteOttService(String serviceId) async {
    try {
      final response = await _apiService.deleteOttService(serviceId);
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to delete OTT service');
      }
    } catch (e) {
      throw Exception('Failed to delete OTT service: $e');
    }
  }
}

// Request Models
class CreateOttServiceRequest {
  final String serviceName;
  final String provider;
  final String? description;
  final double? monthlyCost;
  final String? subscriptionType;
  final DateTime? subscriptionStartDate;
  final DateTime? subscriptionEndDate;
  final String status;

  const CreateOttServiceRequest({
    required this.serviceName,
    required this.provider,
    this.description,
    this.monthlyCost,
    this.subscriptionType,
    this.subscriptionStartDate,
    this.subscriptionEndDate,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'service_name': serviceName,
      'provider': provider,
      'description': description,
      'monthly_cost': monthlyCost,
      'subscription_type': subscriptionType,
      'subscription_start_date': subscriptionStartDate?.toIso8601String(),
      'subscription_end_date': subscriptionEndDate?.toIso8601String(),
      'status': status,
    };
  }
}

class UpdateOttServiceRequest {
  final String? serviceName;
  final String? provider;
  final String? description;
  final double? monthlyCost;
  final String? subscriptionType;
  final DateTime? subscriptionStartDate;
  final DateTime? subscriptionEndDate;
  final String? status;

  const UpdateOttServiceRequest({
    this.serviceName,
    this.provider,
    this.description,
    this.monthlyCost,
    this.subscriptionType,
    this.subscriptionStartDate,
    this.subscriptionEndDate,
    this.status,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (serviceName != null) json['service_name'] = serviceName;
    if (provider != null) json['provider'] = provider;
    if (description != null) json['description'] = description;
    if (monthlyCost != null) json['monthly_cost'] = monthlyCost;
    if (subscriptionType != null) json['subscription_type'] = subscriptionType;
    if (subscriptionStartDate != null) json['subscription_start_date'] = subscriptionStartDate!.toIso8601String();
    if (subscriptionEndDate != null) json['subscription_end_date'] = subscriptionEndDate!.toIso8601String();
    if (status != null) json['status'] = status;
    return json;
  }
}
