import 'package:json_annotation/json_annotation.dart';

part 'custom_screen.g.dart';

@JsonSerializable()
class CustomScreen {
  final String id;
  final String name;
  final String title;
  final String route;
  final String? description;
  final String? icon;
  final List<String> requiredPermissions;
  final List<String> allowedRoles;
  final List<CustomWidget> widgets;
  final Map<String, dynamic>? layout;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;

  const CustomScreen({
    required this.id,
    required this.name,
    required this.title,
    required this.route,
    this.description,
    this.icon,
    required this.requiredPermissions,
    required this.allowedRoles,
    required this.widgets,
    this.layout,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
  });

  factory CustomScreen.fromJson(Map<String, dynamic> json) => _$CustomScreenFromJson(json);
  Map<String, dynamic> toJson() => _$CustomScreenToJson(this);

  CustomScreen copyWith({
    String? id,
    String? name,
    String? title,
    String? route,
    String? description,
    String? icon,
    List<String>? requiredPermissions,
    List<String>? allowedRoles,
    List<CustomWidget>? widgets,
    Map<String, dynamic>? layout,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return CustomScreen(
      id: id ?? this.id,
      name: name ?? this.name,
      title: title ?? this.title,
      route: route ?? this.route,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      requiredPermissions: requiredPermissions ?? this.requiredPermissions,
      allowedRoles: allowedRoles ?? this.allowedRoles,
      widgets: widgets ?? this.widgets,
      layout: layout ?? this.layout,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }
}

@JsonSerializable()
class CustomWidget {
  final String id;
  final String name;
  final String type;
  final String title;
  final Map<String, dynamic> properties;
  final List<String> requiredPermissions;
  final List<String> allowedRoles;
  final Map<String, dynamic>? position;
  final Map<String, dynamic>? styling;
  final bool isVisible;
  final int order;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CustomWidget({
    required this.id,
    required this.name,
    required this.type,
    required this.title,
    required this.properties,
    required this.requiredPermissions,
    required this.allowedRoles,
    this.position,
    this.styling,
    required this.isVisible,
    required this.order,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CustomWidget.fromJson(Map<String, dynamic> json) => _$CustomWidgetFromJson(json);
  Map<String, dynamic> toJson() => _$CustomWidgetToJson(this);

  CustomWidget copyWith({
    String? id,
    String? name,
    String? type,
    String? title,
    Map<String, dynamic>? properties,
    List<String>? requiredPermissions,
    List<String>? allowedRoles,
    Map<String, dynamic>? position,
    Map<String, dynamic>? styling,
    bool? isVisible,
    int? order,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CustomWidget(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      title: title ?? this.title,
      properties: properties ?? this.properties,
      requiredPermissions: requiredPermissions ?? this.requiredPermissions,
      allowedRoles: allowedRoles ?? this.allowedRoles,
      position: position ?? this.position,
      styling: styling ?? this.styling,
      isVisible: isVisible ?? this.isVisible,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum WidgetType {
  statCard,
  chart,
  table,
  form,
  button,
  text,
  image,
  list,
  grid,
  custom;

  String get displayName {
    switch (this) {
      case WidgetType.statCard:
        return 'Stat Card';
      case WidgetType.chart:
        return 'Chart';
      case WidgetType.table:
        return 'Table';
      case WidgetType.form:
        return 'Form';
      case WidgetType.button:
        return 'Button';
      case WidgetType.text:
        return 'Text';
      case WidgetType.image:
        return 'Image';
      case WidgetType.list:
        return 'List';
      case WidgetType.grid:
        return 'Grid';
      case WidgetType.custom:
        return 'Custom';
    }
  }

  String get description {
    switch (this) {
      case WidgetType.statCard:
        return 'Display statistics with icon and value';
      case WidgetType.chart:
        return 'Charts and graphs for data visualization';
      case WidgetType.table:
        return 'Tabular data display';
      case WidgetType.form:
        return 'Input forms for data collection';
      case WidgetType.button:
        return 'Action buttons';
      case WidgetType.text:
        return 'Text content and labels';
      case WidgetType.image:
        return 'Images and media';
      case WidgetType.list:
        return 'List of items';
      case WidgetType.grid:
        return 'Grid layout of items';
      case WidgetType.custom:
        return 'Custom widget implementation';
    }
  }
}
