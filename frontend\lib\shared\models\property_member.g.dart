// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_member.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PropertyMember _$PropertyMemberFromJson(Map<String, dynamic> json) =>
    PropertyMember(
      id: json['id'] as String,
      propertyId: json['property_id'] as String,
      userId: json['user_id'] as String,
      role: json['role'] as String?,
      position: json['position'] as String?,
      department: json['department'] as String?,
      hourlyRate: (json['hourly_rate'] as num?)?.toDouble(),
      startDate: json['start_date'] == null
          ? null
          : DateTime.parse(json['start_date'] as String),
      endDate: json['end_date'] == null
          ? null
          : DateTime.parse(json['end_date'] as String),
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$PropertyMemberToJson(PropertyMember instance) =>
    <String, dynamic>{
      'id': instance.id,
      'property_id': instance.propertyId,
      'user_id': instance.userId,
      'role': instance.role,
      'position': instance.position,
      'department': instance.department,
      'hourly_rate': instance.hourlyRate,
      'start_date': instance.startDate?.toIso8601String(),
      'end_date': instance.endDate?.toIso8601String(),
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
    };
