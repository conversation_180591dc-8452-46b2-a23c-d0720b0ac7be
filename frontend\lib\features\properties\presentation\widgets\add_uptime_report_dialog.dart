import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/uptime_report.dart';
import '../providers/uptime_reports_providers.dart';

class AddUptimeReportDialog extends ConsumerStatefulWidget {
  final String propertyId;
  final UptimeReport? report; // For editing
  final VoidCallback? onReportAdded;

  const AddUptimeReportDialog({
    super.key,
    required this.propertyId,
    this.report,
    this.onReportAdded,
  });

  @override
  ConsumerState<AddUptimeReportDialog> createState() => _AddUptimeReportDialogState();
}

class _AddUptimeReportDialogState extends ConsumerState<AddUptimeReportDialog> {
  final _formKey = GlobalKey<FormState>();
  final _uptimePercentageController = TextEditingController();
  final _downtimeMinutesController = TextEditingController();
  final _incidentsCountController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedServiceType = 'electricity';
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  bool _autoCalculateDowntime = true;

  final List<String> _serviceTypes = [
    'electricity',
    'internet',
    'water',
    'generator',
    'hvac',
    'security',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.report != null) {
      _populateFields();
    }
    
    // Add listener for auto-calculation
    _uptimePercentageController.addListener(_calculateDowntime);
  }

  void _populateFields() {
    final report = widget.report!;
    _selectedServiceType = report.serviceType;
    _selectedDate = report.date;
    _uptimePercentageController.text = report.uptimePercentage?.toString() ?? '';
    _downtimeMinutesController.text = report.downtimeMinutes?.toString() ?? '';
    _incidentsCountController.text = report.incidentsCount?.toString() ?? '';
    _notesController.text = report.notes ?? '';
  }

  void _calculateDowntime() {
    if (!_autoCalculateDowntime) return;
    
    final uptimePercentage = double.tryParse(_uptimePercentageController.text);
    
    if (uptimePercentage != null && uptimePercentage >= 0 && uptimePercentage <= 100) {
      const totalMinutesInDay = 24 * 60; // 1440 minutes
      final downtimePercentage = 100 - uptimePercentage;
      final downtimeMinutes = ((downtimePercentage / 100) * totalMinutesInDay).round();
      _downtimeMinutesController.text = downtimeMinutes.toString();
    }
  }

  @override
  void dispose() {
    _uptimePercentageController.dispose();
    _downtimeMinutesController.dispose();
    _incidentsCountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.report != null;

    return AlertDialog(
      title: Text(isEditing ? 'Edit Uptime Report' : 'Add Uptime Report'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Service Type
                DropdownButtonFormField<String>(
                  value: _selectedServiceType,
                  decoration: const InputDecoration(
                    labelText: 'Service Type *',
                    border: OutlineInputBorder(),
                  ),
                  items: _serviceTypes.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Row(
                        children: [
                          Icon(_getServiceIcon(type), size: 20),
                          const SizedBox(width: 8),
                          Text(_getServiceDisplayName(type)),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedServiceType = value!;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Date
                InkWell(
                  onTap: () => _selectDate(context),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Report Date *',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      AppUtils.formatDate(_selectedDate),
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Uptime Percentage and Downtime Minutes Row
                Row(
                  children: [
                    // Uptime Percentage
                    Expanded(
                      child: TextFormField(
                        controller: _uptimePercentageController,
                        decoration: const InputDecoration(
                          labelText: 'Uptime %',
                          hintText: '99.5',
                          suffixText: '%',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final percentage = double.tryParse(value);
                            if (percentage == null || percentage < 0 || percentage > 100) {
                              return 'Enter 0-100';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),

                    // Downtime Minutes
                    Expanded(
                      child: TextFormField(
                        controller: _downtimeMinutesController,
                        decoration: InputDecoration(
                          labelText: 'Downtime',
                          hintText: '30',
                          suffixText: 'min',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _autoCalculateDowntime ? Icons.calculate : Icons.edit,
                              size: 20,
                            ),
                            onPressed: () {
                              setState(() {
                                _autoCalculateDowntime = !_autoCalculateDowntime;
                              });
                              if (_autoCalculateDowntime) {
                                _calculateDowntime();
                              }
                            },
                            tooltip: _autoCalculateDowntime ? 'Manual entry' : 'Auto calculate',
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        readOnly: _autoCalculateDowntime,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final minutes = int.tryParse(value);
                            if (minutes == null || minutes < 0 || minutes > 1440) {
                              return 'Enter 0-1440';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Incidents Count
                TextFormField(
                  controller: _incidentsCountController,
                  decoration: const InputDecoration(
                    labelText: 'Incidents Count',
                    hintText: '0',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final count = int.tryParse(value);
                      if (count == null || count < 0) {
                        return 'Enter a valid number';
                      }
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Notes
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    hintText: 'Optional notes about service performance',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),

                // Auto-calculate info
                if (_autoCalculateDowntime) ...[
                  const SizedBox(height: AppConstants.smallPadding),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info,
                          size: 16,
                          color: Colors.blue[700],
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Downtime is automatically calculated from uptime percentage',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveReport,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (selectedDate != null) {
      setState(() {
        _selectedDate = selectedDate;
      });
    }
  }

  IconData _getServiceIcon(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'internet':
        return Icons.wifi;
      case 'water':
        return Icons.water_drop;
      case 'generator':
        return Icons.power;
      case 'hvac':
        return Icons.air;
      case 'security':
        return Icons.security;
      default:
        return Icons.miscellaneous_services;
    }
  }

  String _getServiceDisplayName(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'electricity':
        return 'Electricity';
      case 'internet':
        return 'Internet';
      case 'water':
        return 'Water';
      case 'generator':
        return 'Generator';
      case 'hvac':
        return 'HVAC';
      case 'security':
        return 'Security System';
      default:
        return serviceType;
    }
  }

  Future<void> _saveReport() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final uptimePercentage = _uptimePercentageController.text.isNotEmpty
          ? double.tryParse(_uptimePercentageController.text)
          : null;
      final downtimeMinutes = _downtimeMinutesController.text.isNotEmpty
          ? int.tryParse(_downtimeMinutesController.text)
          : null;
      final incidentsCount = _incidentsCountController.text.isNotEmpty
          ? int.tryParse(_incidentsCountController.text)
          : null;

      if (widget.report != null) {
        // Update existing report
        final request = UpdateUptimeReportRequest(
          serviceType: _selectedServiceType,
          date: _selectedDate,
          uptimePercentage: uptimePercentage,
          downtimeMinutes: downtimeMinutes,
          incidentsCount: incidentsCount,
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
        );

        await ref.read(uptimeReportsNotifierProvider(widget.propertyId).notifier)
            .updateUptimeReport(widget.report!.id, request);
      } else {
        // Create new report
        final request = CreateUptimeReportRequest(
          serviceType: _selectedServiceType,
          date: _selectedDate,
          uptimePercentage: uptimePercentage,
          downtimeMinutes: downtimeMinutes,
          incidentsCount: incidentsCount,
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
        );

        await ref.read(uptimeReportsNotifierProvider(widget.propertyId).notifier)
            .addUptimeReport(request);
      }

      if (mounted) {
        Navigator.of(context).pop();
        widget.onReportAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to save uptime report: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
