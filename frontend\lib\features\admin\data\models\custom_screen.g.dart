// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'custom_screen.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomScreen _$CustomScreenFromJson(Map<String, dynamic> json) => CustomScreen(
      id: json['id'] as String,
      name: json['name'] as String,
      title: json['title'] as String,
      route: json['route'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      requiredPermissions: (json['requiredPermissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      allowedRoles: (json['allowedRoles'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      widgets: (json['widgets'] as List<dynamic>)
          .map((e) => CustomWidget.fromJson(e as Map<String, dynamic>))
          .toList(),
      layout: json['layout'] as Map<String, dynamic>?,
      isActive: json['isActive'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String?,
    );

Map<String, dynamic> _$CustomScreenToJson(CustomScreen instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'title': instance.title,
      'route': instance.route,
      'description': instance.description,
      'icon': instance.icon,
      'requiredPermissions': instance.requiredPermissions,
      'allowedRoles': instance.allowedRoles,
      'widgets': instance.widgets,
      'layout': instance.layout,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'createdBy': instance.createdBy,
    };

CustomWidget _$CustomWidgetFromJson(Map<String, dynamic> json) => CustomWidget(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      properties: json['properties'] as Map<String, dynamic>,
      requiredPermissions: (json['requiredPermissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      allowedRoles: (json['allowedRoles'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      position: json['position'] as Map<String, dynamic>?,
      styling: json['styling'] as Map<String, dynamic>?,
      isVisible: json['isVisible'] as bool,
      order: (json['order'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CustomWidgetToJson(CustomWidget instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'title': instance.title,
      'properties': instance.properties,
      'requiredPermissions': instance.requiredPermissions,
      'allowedRoles': instance.allowedRoles,
      'position': instance.position,
      'styling': instance.styling,
      'isVisible': instance.isVisible,
      'order': instance.order,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
