import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/user.dart';
import '../../../shared/models/property_member.dart';
import '../../../features/auth/presentation/providers/auth_providers.dart';

class PropertyAccessService {
  final Ref ref;

  PropertyAccessService(this.ref);

  /// Check if current user has access to a specific property
  Future<bool> hasPropertyAccess(String propertyId) async {
    final user = await _getCurrentUser();
    if (user == null) return false;

    // Admin users have access to all properties
    if (user.isAdmin) return true;

    // Check if user is a member of this property
    final userProperties = await getUserProperties(user.id);
    return userProperties.any((member) =>
      member.propertyId == propertyId && member.isCurrentlyActive
    );
  }

  /// Get all properties the current user has access to
  Future<List<String>> getUserAccessiblePropertyIds() async {
    final user = await _getCurrentUser();
    if (user == null) return [];

    // Admin users have access to all properties
    if (user.isAdmin) {
      // TODO: Fetch all property IDs from properties service
      return [];
    }

    // Get user's property memberships
    final userProperties = await getUserProperties(user.id);
    return userProperties
        .where((member) => member.isCurrentlyActive)
        .map((member) => member.propertyId)
        .toList();
  }

  /// Get user's property memberships
  Future<List<PropertyMember>> getUserProperties(String userId) async {
    // TODO: Implement API call to get user's property memberships
    // For now, return empty list
    return [];
  }

  /// Check if user has specific permission for a property
  Future<bool> hasPropertyPermission(String propertyId, String permission) async {
    final user = await _getCurrentUser();
    if (user == null) return false;

    // Admin users have all permissions
    if (user.isAdmin) return true;

    // Check if user has access to the property first
    final hasAccess = await hasPropertyAccess(propertyId);
    if (!hasAccess) return false;

    // Check if user has the specific permission
    return user.hasPermission(permission);
  }

  /// Filter data based on user's property access
  Future<List<T>> filterByPropertyAccess<T>(
    List<T> items,
    String Function(T) getPropertyId,
  ) async {
    final user = await _getCurrentUser();
    if (user == null) return [];

    // Admin users see all data
    if (user.isAdmin) return items;

    // Get accessible property IDs
    final accessiblePropertyIds = await getUserAccessiblePropertyIds();

    // Filter items by accessible properties
    return items.where((item) {
      final propertyId = getPropertyId(item);
      return accessiblePropertyIds.contains(propertyId);
    }).toList();
  }

  /// Get current user from auth state
  Future<User?> _getCurrentUser() async {
    final authState = ref.read(authStateProvider);
    return authState.user;
  }

  /// Check if user can create data for a specific property
  Future<bool> canCreateForProperty(String propertyId, String permission) async {
    return hasPropertyPermission(propertyId, permission);
  }

  /// Check if user can update data for a specific property
  Future<bool> canUpdateForProperty(String propertyId, String permission) async {
    return hasPropertyPermission(propertyId, permission);
  }

  /// Check if user can delete data for a specific property
  Future<bool> canDeleteForProperty(String propertyId, String permission) async {
    return hasPropertyPermission(propertyId, permission);
  }

  /// Get user's role for a specific property
  Future<String?> getUserRoleForProperty(String propertyId) async {
    final user = await _getCurrentUser();
    if (user == null) return null;

    // Admin users have admin role everywhere
    if (user.isAdmin) return 'admin';

    // Get user's property membership
    final userProperties = await getUserProperties(user.id);
    final membership = userProperties.firstWhere(
      (member) => member.propertyId == propertyId && member.isCurrentlyActive,
      orElse: () => throw StateError('No membership found'),
    );

    return membership.role;
  }

  /// Check if user is property manager for a specific property
  Future<bool> isPropertyManager(String propertyId) async {
    final role = await getUserRoleForProperty(propertyId);
    return role == 'property_manager' || role == 'admin';
  }

  /// Check if user is property supervisor for a specific property
  Future<bool> isPropertySupervisor(String propertyId) async {
    final role = await getUserRoleForProperty(propertyId);
    return role == 'supervisor' || role == 'property_manager' || role == 'admin';
  }
}

// Provider for PropertyAccessService
final propertyAccessServiceProvider = Provider<PropertyAccessService>((ref) {
  return PropertyAccessService(ref);
});

// Provider to check property access
final propertyAccessProvider = FutureProvider.family<bool, String>((ref, propertyId) async {
  final service = ref.read(propertyAccessServiceProvider);
  return service.hasPropertyAccess(propertyId);
});

// Provider to get user's accessible property IDs
final userAccessiblePropertiesProvider = FutureProvider<List<String>>((ref) async {
  final service = ref.read(propertyAccessServiceProvider);
  return service.getUserAccessiblePropertyIds();
});

// Provider to check property permission
final propertyPermissionProvider = FutureProvider.family<bool, PropertyPermissionRequest>((ref, request) async {
  final service = ref.read(propertyAccessServiceProvider);
  return service.hasPropertyPermission(request.propertyId, request.permission);
});

class PropertyPermissionRequest {
  final String propertyId;
  final String permission;

  const PropertyPermissionRequest({
    required this.propertyId,
    required this.permission,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyPermissionRequest &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          permission == other.permission;

  @override
  int get hashCode => propertyId.hashCode ^ permission.hashCode;
}
