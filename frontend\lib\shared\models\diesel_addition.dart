import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'diesel_addition.g.dart';

@JsonSerializable()
class DieselAddition {
  final String id;
  @Json<PERSON>ey(name: 'property_id')
  final String propertyId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'quantity_liters')
  final double quantity;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'cost_per_liter')
  final double? costPerLiter;
  @<PERSON>son<PERSON>ey(name: 'total_cost')
  final double? totalCost;
  final String? supplier;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'receipt_number')
  final String? receiptNumber;
  final String? notes;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'added_by')
  final String addedBy;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'added_at')
  final DateTime addedAt;
  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime? updatedAt;

  const DieselAddition({
    required this.id,
    required this.propertyId,
    required this.quantity,
    this.costPerLiter,
    this.totalCost,
    this.supplier,
    this.receiptNumber,
    this.notes,
    required this.addedBy,
    required this.addedAt,
    required this.createdAt,
    this.updatedAt,
  });

  factory DieselAddition.fromJson(Map<String, dynamic> json) => _$DieselAdditionFromJson(json);
  Map<String, dynamic> toJson() => _$DieselAdditionToJson(this);

  DieselAddition copyWith({
    String? id,
    String? propertyId,
    double? quantity,
    double? costPerLiter,
    double? totalCost,
    String? supplier,
    String? receiptNumber,
    String? notes,
    String? addedBy,
    DateTime? addedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DieselAddition(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      quantity: quantity ?? this.quantity,
      costPerLiter: costPerLiter ?? this.costPerLiter,
      totalCost: totalCost ?? this.totalCost,
      supplier: supplier ?? this.supplier,
      receiptNumber: receiptNumber ?? this.receiptNumber,
      notes: notes ?? this.notes,
      addedBy: addedBy ?? this.addedBy,
      addedAt: addedAt ?? this.addedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DieselAddition &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'DieselAddition{id: $id, quantity: $quantity, supplier: $supplier}';
  }

  // Business logic methods
  String get quantityDisplayText => '${quantity.toStringAsFixed(1)}L';

  String get costDisplayText {
    if (totalCost != null) {
      return '\$${totalCost!.toStringAsFixed(2)}';
    } else if (costPerLiter != null) {
      final total = quantity * costPerLiter!;
      return '\$${total.toStringAsFixed(2)}';
    }
    return 'Cost not specified';
  }

  String get costPerLiterDisplayText {
    if (costPerLiter != null) {
      return '\$${costPerLiter!.toStringAsFixed(2)}/L';
    }
    return 'Not specified';
  }

  double? get calculatedTotalCost {
    if (totalCost != null) return totalCost;
    if (costPerLiter != null) return quantity * costPerLiter!;
    return null;
  }

  String get supplierDisplayName {
    if (supplier == null || supplier!.isEmpty) return 'Unknown Supplier';
    return supplier!;
  }

  IconData get supplierIcon {
    if (supplier == null) return Icons.local_gas_station;
    
    switch (supplier!.toLowerCase()) {
      case 'shell':
        return Icons.local_gas_station;
      case 'bp':
      case 'british petroleum':
        return Icons.local_gas_station;
      case 'exxon':
      case 'exxonmobil':
        return Icons.local_gas_station;
      case 'chevron':
        return Icons.local_gas_station;
      case 'total':
        return Icons.local_gas_station;
      default:
        return Icons.local_gas_station;
    }
  }

  Color get quantityColor {
    if (quantity >= 1000) return Colors.green;
    if (quantity >= 500) return Colors.orange;
    return Colors.red;
  }

  String get addedAtFormatted => _formatDateTime(addedAt);
  String get createdAtFormatted => _formatDateTime(createdAt);

  String get addedAtDateOnly => _formatDate(addedAt);
  String get addedAtTimeOnly => _formatTime(addedAt);

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${_formatTime(dateTime)}';
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  bool get hasReceiptNumber => receiptNumber != null && receiptNumber!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasCostInformation => costPerLiter != null || totalCost != null;

  String get receiptDisplayText {
    if (!hasReceiptNumber) return 'No receipt';
    return 'Receipt: $receiptNumber';
  }

  // Time-based properties
  bool get isToday {
    final now = DateTime.now();
    return addedAt.year == now.year &&
           addedAt.month == now.month &&
           addedAt.day == now.day;
  }

  bool get isThisWeek {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));
    return addedAt.isAfter(weekAgo);
  }

  bool get isThisMonth {
    final now = DateTime.now();
    return addedAt.year == now.year && addedAt.month == now.month;
  }

  String get timeAgoText {
    final now = DateTime.now();
    final difference = now.difference(addedAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  // Validation methods
  bool get isValid => quantity > 0 && addedBy.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    
    if (quantity <= 0) errors.add('Quantity must be greater than 0');
    if (addedBy.isEmpty) errors.add('Added by is required');
    
    if (costPerLiter != null && costPerLiter! < 0) {
      errors.add('Cost per liter cannot be negative');
    }
    
    if (totalCost != null && totalCost! < 0) {
      errors.add('Total cost cannot be negative');
    }
    
    // Check if both cost per liter and total cost are provided and they don't match
    if (costPerLiter != null && totalCost != null) {
      final calculatedTotal = quantity * costPerLiter!;
      final difference = (calculatedTotal - totalCost!).abs();
      if (difference > 0.01) { // Allow for small rounding differences
        errors.add('Total cost does not match quantity × cost per liter');
      }
    }
    
    return errors;
  }

  // Cost efficiency methods
  double? get costEfficiencyScore {
    if (costPerLiter == null) return null;
    
    // This is a simple scoring system - in reality, you'd compare against market rates
    // Lower cost per liter = higher score
    const maxReasonableCost = 2.0; // $2.00 per liter as baseline
    const minReasonableCost = 0.5; // $0.50 per liter as baseline
    
    if (costPerLiter! <= minReasonableCost) return 1.0;
    if (costPerLiter! >= maxReasonableCost) return 0.0;
    
    return 1.0 - ((costPerLiter! - minReasonableCost) / (maxReasonableCost - minReasonableCost));
  }

  String get costEfficiencyText {
    final score = costEfficiencyScore;
    if (score == null) return 'Unknown';
    
    if (score >= 0.8) return 'Excellent';
    if (score >= 0.6) return 'Good';
    if (score >= 0.4) return 'Average';
    if (score >= 0.2) return 'Poor';
    return 'Very Poor';
  }

  Color get costEfficiencyColor {
    final score = costEfficiencyScore;
    if (score == null) return Colors.grey;
    
    if (score >= 0.8) return Colors.green;
    if (score >= 0.6) return Colors.lightGreen;
    if (score >= 0.4) return Colors.orange;
    if (score >= 0.2) return Colors.deepOrange;
    return Colors.red;
  }

  // Summary text for cards/lists
  String get summaryText {
    final parts = <String>[];
    parts.add(quantityDisplayText);
    
    if (hasCostInformation) {
      parts.add(costDisplayText);
    }
    
    if (supplier != null && supplier!.isNotEmpty) {
      parts.add('from $supplier');
    }
    
    return parts.join(' • ');
  }
}
