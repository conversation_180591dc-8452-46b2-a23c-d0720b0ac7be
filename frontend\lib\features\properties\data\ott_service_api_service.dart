import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../shared/models/api_response.dart';

part 'ott_service_api_service.g.dart';

@RestApi()
abstract class OttServiceApiService {
  factory OttServiceApiService(Dio dio) = _OttServiceApiService;

  @GET('/api/ott-services/{propertyId}')
  Future<ApiResponse<List<OttService>>> getOttServices(@Path('propertyId') String propertyId);

  @POST('/api/ott-services/{propertyId}')
  Future<ApiResponse<OttService>> createOttService(
    @Path('propertyId') String propertyId,
    @Body() CreateOttServiceRequest request,
  );

  @GET('/api/ott-services/{id}')
  Future<ApiResponse<OttService>> getOttServiceById(@Path('id') String id);

  @PUT('/api/ott-services/{id}')
  Future<ApiResponse<OttService>> updateOttService(
    @Path('id') String id,
    @Body() UpdateOttServiceRequest request,
  );

  @DELETE('/api/ott-services/{id}')
  Future<ApiResponse<VoidResponse>> deleteOttService(@Path('id') String id);
}

// Models
class OttService {
  final String id;
  @JsonKey(name: 'property_id')
  final String propertyId;
  @JsonKey(name: 'service_name')
  final String serviceName;
  @JsonKey(name: 'subscription_type')
  final String? subscriptionType;
  @JsonKey(name: 'monthly_cost')
  final double? monthlyCost;
  @JsonKey(name: 'renewal_date')
  final DateTime? renewalDate;
  final String status;
  @JsonKey(name: 'login_credentials')
  final Map<String, dynamic>? loginCredentials;
  final String? notes;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const OttService({
    required this.id,
    required this.propertyId,
    required this.serviceName,
    this.subscriptionType,
    this.monthlyCost,
    this.renewalDate,
    required this.status,
    this.loginCredentials,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OttService.fromJson(Map<String, dynamic> json) => OttService(
        id: json['id'] as String,
        propertyId: json['property_id'] as String,
        serviceName: json['service_name'] as String,
        subscriptionType: json['subscription_type'] as String?,
        monthlyCost: json['monthly_cost'] != null ? (json['monthly_cost'] as num).toDouble() : null,
        renewalDate: json['renewal_date'] != null ? DateTime.parse(json['renewal_date'] as String) : null,
        status: json['status'] as String,
        loginCredentials: json['login_credentials'] as Map<String, dynamic>?,
        notes: json['notes'] as String?,
        createdAt: DateTime.parse(json['created_at'] as String),
        updatedAt: DateTime.parse(json['updated_at'] as String),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'property_id': propertyId,
        'service_name': serviceName,
        'subscription_type': subscriptionType,
        'monthly_cost': monthlyCost,
        'renewal_date': renewalDate?.toIso8601String(),
        'status': status,
        'login_credentials': loginCredentials,
        'notes': notes,
        'created_at': createdAt.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
      };
}

// Request models
class CreateOttServiceRequest {
  @JsonKey(name: 'service_name')
  final String serviceName;
  @JsonKey(name: 'subscription_type')
  final String? subscriptionType;
  @JsonKey(name: 'monthly_cost')
  final double? monthlyCost;
  @JsonKey(name: 'renewal_date')
  final DateTime? renewalDate;
  final String status;
  @JsonKey(name: 'login_credentials')
  final Map<String, dynamic>? loginCredentials;
  final String? notes;

  const CreateOttServiceRequest({
    required this.serviceName,
    this.subscriptionType,
    this.monthlyCost,
    this.renewalDate,
    this.status = 'active',
    this.loginCredentials,
    this.notes,
  });

  Map<String, dynamic> toJson() => {
        'service_name': serviceName,
        if (subscriptionType != null) 'subscription_type': subscriptionType,
        if (monthlyCost != null) 'monthly_cost': monthlyCost,
        if (renewalDate != null) 'renewal_date': renewalDate!.toIso8601String(),
        'status': status,
        if (loginCredentials != null) 'login_credentials': loginCredentials,
        if (notes != null) 'notes': notes,
      };
}

class UpdateOttServiceRequest {
  @JsonKey(name: 'service_name')
  final String? serviceName;
  @JsonKey(name: 'subscription_type')
  final String? subscriptionType;
  @JsonKey(name: 'monthly_cost')
  final double? monthlyCost;
  @JsonKey(name: 'renewal_date')
  final DateTime? renewalDate;
  final String? status;
  @JsonKey(name: 'login_credentials')
  final Map<String, dynamic>? loginCredentials;
  final String? notes;

  const UpdateOttServiceRequest({
    this.serviceName,
    this.subscriptionType,
    this.monthlyCost,
    this.renewalDate,
    this.status,
    this.loginCredentials,
    this.notes,
  });

  Map<String, dynamic> toJson() => {
        if (serviceName != null) 'service_name': serviceName,
        if (subscriptionType != null) 'subscription_type': subscriptionType,
        if (monthlyCost != null) 'monthly_cost': monthlyCost,
        if (renewalDate != null) 'renewal_date': renewalDate!.toIso8601String(),
        if (status != null) 'status': status,
        if (loginCredentials != null) 'login_credentials': loginCredentials,
        if (notes != null) 'notes': notes,
      };
}
