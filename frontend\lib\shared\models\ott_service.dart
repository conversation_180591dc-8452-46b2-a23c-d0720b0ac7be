import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'ott_service.g.dart';

@JsonSerializable()
class OttService {
  final String id;
  @Json<PERSON>ey(name: 'property_id')
  final String propertyId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'service_name')
  final String serviceName;
  final String provider;
  final String? description;
  @Json<PERSON>ey(name: 'monthly_cost')
  final double? monthlyCost;
  @Json<PERSON>ey(name: 'subscription_type')
  final String? subscriptionType;
  @<PERSON>son<PERSON><PERSON>(name: 'subscription_start_date')
  final DateTime? subscriptionStartDate;
  @Json<PERSON>ey(name: 'subscription_end_date')
  final DateTime? subscriptionEndDate;
  final String status;
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime? updatedAt;

  const OttService({
    required this.id,
    required this.propertyId,
    required this.serviceName,
    required this.provider,
    this.description,
    this.monthlyCost,
    this.subscriptionType,
    this.subscriptionStartDate,
    this.subscriptionEndDate,
    required this.status,
    required this.createdAt,
    this.updatedAt,
  });

  factory OttService.fromJson(Map<String, dynamic> json) => _$OttServiceFromJson(json);
  Map<String, dynamic> toJson() => _$OttServiceToJson(this);

  OttService copyWith({
    String? id,
    String? propertyId,
    String? serviceName,
    String? provider,
    String? description,
    double? monthlyCost,
    String? subscriptionType,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionEndDate,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OttService(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      serviceName: serviceName ?? this.serviceName,
      provider: provider ?? this.provider,
      description: description ?? this.description,
      monthlyCost: monthlyCost ?? this.monthlyCost,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionStartDate: subscriptionStartDate ?? this.subscriptionStartDate,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OttService &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OttService{id: $id, serviceName: $serviceName, provider: $provider, status: $status}';
  }

  // Business logic methods
  bool get isActive => status.toLowerCase() == 'active';
  bool get isInactive => status.toLowerCase() == 'inactive';
  bool get isUnderMaintenance => status.toLowerCase() == 'maintenance';

  Color get statusColor {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.red;
      case 'maintenance':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String get statusDisplayName {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'maintenance':
        return 'Under Maintenance';
      default:
        return status;
    }
  }

  IconData get serviceIcon {
    switch (serviceName.toLowerCase()) {
      case 'netflix':
        return Icons.movie;
      case 'amazon prime':
      case 'prime video':
        return Icons.video_library;
      case 'disney+':
      case 'disney plus':
        return Icons.child_friendly;
      case 'hulu':
        return Icons.tv;
      case 'youtube':
      case 'youtube premium':
        return Icons.play_circle;
      case 'spotify':
        return Icons.music_note;
      case 'apple tv':
      case 'apple tv+':
        return Icons.apple;
      default:
        return Icons.tv;
    }
  }

  String get providerDisplayName {
    switch (provider.toLowerCase()) {
      case 'netflix':
        return 'Netflix';
      case 'amazon':
      case 'amazon prime':
        return 'Amazon Prime';
      case 'disney':
      case 'disney+':
        return 'Disney+';
      case 'hulu':
        return 'Hulu';
      case 'youtube':
        return 'YouTube';
      case 'spotify':
        return 'Spotify';
      case 'apple':
        return 'Apple';
      default:
        return provider;
    }
  }

  String get subscriptionTypeDisplayName {
    if (subscriptionType == null) return 'Unknown';
    switch (subscriptionType!.toLowerCase()) {
      case 'monthly':
        return 'Monthly';
      case 'yearly':
        return 'Yearly';
      case 'quarterly':
        return 'Quarterly';
      case 'lifetime':
        return 'Lifetime';
      default:
        return subscriptionType!;
    }
  }

  bool get hasSubscriptionDates {
    return subscriptionStartDate != null || subscriptionEndDate != null;
  }

  bool get isSubscriptionActive {
    if (!hasSubscriptionDates) return isActive;
    
    final now = DateTime.now();
    
    // Check if subscription has started
    if (subscriptionStartDate != null && subscriptionStartDate!.isAfter(now)) {
      return false;
    }
    
    // Check if subscription has expired
    if (subscriptionEndDate != null && subscriptionEndDate!.isBefore(now)) {
      return false;
    }
    
    return isActive;
  }

  String get subscriptionStatus {
    if (!hasSubscriptionDates) return statusDisplayName;
    
    final now = DateTime.now();
    
    if (subscriptionStartDate != null && subscriptionStartDate!.isAfter(now)) {
      return 'Not Started';
    }
    
    if (subscriptionEndDate != null && subscriptionEndDate!.isBefore(now)) {
      return 'Expired';
    }
    
    return 'Active';
  }

  int? get daysUntilExpiry {
    if (subscriptionEndDate == null) return null;
    
    final now = DateTime.now();
    final difference = subscriptionEndDate!.difference(now);
    
    return difference.inDays;
  }

  bool get isExpiringSoon {
    final days = daysUntilExpiry;
    return days != null && days <= 30 && days > 0;
  }

  bool get isExpired {
    final days = daysUntilExpiry;
    return days != null && days < 0;
  }

  String get costDisplayText {
    if (monthlyCost == null) return 'Cost not specified';
    return '\$${monthlyCost!.toStringAsFixed(2)}/${subscriptionTypeDisplayName.toLowerCase()}';
  }

  String get subscriptionPeriodText {
    if (!hasSubscriptionDates) return 'No subscription period';
    
    final start = subscriptionStartDate;
    final end = subscriptionEndDate;
    
    if (start != null && end != null) {
      return '${_formatDate(start)} - ${_formatDate(end)}';
    } else if (start != null) {
      return 'Started: ${_formatDate(start)}';
    } else if (end != null) {
      return 'Expires: ${_formatDate(end)}';
    }
    
    return 'No subscription period';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Validation methods
  bool get isValid => serviceName.isNotEmpty && provider.isNotEmpty && status.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    if (serviceName.isEmpty) errors.add('Service name is required');
    if (provider.isEmpty) errors.add('Provider is required');
    if (status.isEmpty) errors.add('Status is required');
    
    const validStatuses = ['active', 'inactive', 'maintenance'];
    if (!validStatuses.contains(status.toLowerCase())) {
      errors.add('Invalid status');
    }
    
    if (monthlyCost != null && monthlyCost! < 0) {
      errors.add('Monthly cost cannot be negative');
    }
    
    if (subscriptionStartDate != null && subscriptionEndDate != null) {
      if (subscriptionStartDate!.isAfter(subscriptionEndDate!)) {
        errors.add('Subscription start date cannot be after end date');
      }
    }
    
    return errors;
  }
}
