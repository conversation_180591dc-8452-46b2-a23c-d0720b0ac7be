import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getQueryParams, handleError, corsHeaders } from '@/lib/utils';

async function getUserAttendanceReportHandler(
  request: NextRequest, 
  context: { params: { userId: string } }, 
  currentUser: any
) {
  try {
    const { userId } = context.params;
    const params = getQueryParams(request);
    const { start_date, end_date, property_id } = params;

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        fullName: true,
        email: true,
        role: true,
      },
    });

    if (!user) {
      return Response.json(
        createApiResponse(null, 'User not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Build date range filter
    const dateFilter: any = {};
    if (start_date && end_date) {
      dateFilter.date = {
        gte: new Date(start_date),
        lte: new Date(end_date),
      };
    } else if (start_date) {
      dateFilter.date = {
        gte: new Date(start_date),
      };
    } else if (end_date) {
      dateFilter.date = {
        lte: new Date(end_date),
      };
    }

    // Build property filter
    const propertyFilter: any = {};
    if (property_id) {
      propertyFilter.propertyId = property_id;
    }

    // Combine filters
    const where = { 
      userId,
      ...dateFilter, 
      ...propertyFilter 
    };

    // Get attendance records for the user
    const attendanceRecords = await prisma.attendance.findMany({
      where,
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: {
        date: 'desc',
      },
    });

    // Calculate user statistics
    const totalRecords = attendanceRecords.length;
    const totalHours = attendanceRecords.reduce((sum, record) => {
      if (record.hoursWorked) {
        return sum + parseFloat(record.hoursWorked.toString());
      }
      return sum;
    }, 0);
    const averageHours = totalRecords > 0 ? totalHours / totalRecords : 0;

    // Calculate status breakdown
    const statusBreakdown = attendanceRecords.reduce((acc, record) => {
      const status = record.status.toLowerCase();
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Group by property
    const byProperty = attendanceRecords.reduce((acc, record) => {
      const propertyId = record.propertyId;
      if (!acc[propertyId]) {
        acc[propertyId] = {
          property_id: propertyId,
          property_name: record.property.name,
          property_type: record.property.type,
          total_records: 0,
          status_breakdown: {},
          total_hours: 0,
        };
      }
      
      acc[propertyId].total_records++;
      
      const status = record.status.toLowerCase();
      acc[propertyId].status_breakdown[status] = (acc[propertyId].status_breakdown[status] || 0) + 1;
      
      if (record.hoursWorked) {
        acc[propertyId].total_hours += parseFloat(record.hoursWorked.toString());
      }
      
      return acc;
    }, {} as Record<string, any>);

    // Convert property data to array and add averages
    const propertyStats = Object.values(byProperty).map((prop: any) => ({
      ...prop,
      average_hours: prop.total_records > 0 ? prop.total_hours / prop.total_records : 0,
    }));

    // Group by month for trend analysis
    const monthlyStats = attendanceRecords.reduce((acc, record) => {
      const month = record.date.toISOString().substring(0, 7); // YYYY-MM format
      if (!acc[month]) {
        acc[month] = {
          month,
          total_records: 0,
          status_breakdown: {},
          total_hours: 0,
        };
      }
      
      acc[month].total_records++;
      
      const status = record.status.toLowerCase();
      acc[month].status_breakdown[status] = (acc[month].status_breakdown[status] || 0) + 1;
      
      if (record.hoursWorked) {
        acc[month].total_hours += parseFloat(record.hoursWorked.toString());
      }
      
      return acc;
    }, {} as Record<string, any>);

    // Convert monthly data to array and add averages
    const monthlyTrends = Object.values(monthlyStats)
      .map((month: any) => ({
        ...month,
        average_hours: month.total_records > 0 ? month.total_hours / month.total_records : 0,
      }))
      .sort((a: any, b: any) => a.month.localeCompare(b.month));

    // Transform attendance records for response
    const transformedRecords = attendanceRecords.map(record => ({
      id: record.id,
      property_id: record.propertyId,
      property_name: record.property.name,
      property_type: record.property.type,
      date: record.date,
      check_in_time: record.checkInTime,
      check_out_time: record.checkOutTime,
      hours_worked: record.hoursWorked ? parseFloat(record.hoursWorked.toString()) : null,
      status: record.status.toLowerCase(),
      notes: record.notes,
      created_at: record.createdAt,
      updated_at: record.updatedAt,
    }));

    const report = {
      user: {
        id: user.id,
        full_name: user.fullName,
        email: user.email,
        role: user.role.toLowerCase(),
      },
      period: {
        start_date: start_date || null,
        end_date: end_date || null,
        property_id: property_id || null,
      },
      summary: {
        total_records: totalRecords,
        total_hours: totalHours,
        average_hours: averageHours,
        properties_worked: propertyStats.length,
      },
      status_breakdown: statusBreakdown,
      by_property: propertyStats,
      monthly_trends: monthlyTrends,
      attendance_records: transformedRecords,
      generated_at: new Date().toISOString(),
    };

    return Response.json(
      createApiResponse(report),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to generate user attendance report');
  }
}

export const GET = requireAuth(getUserAttendanceReportHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
