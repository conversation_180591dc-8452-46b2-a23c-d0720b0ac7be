import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const updateUptimeReportSchema = Joi.object({
  service_type: Joi.string().min(2).max(100).optional(),
  date: Joi.date().optional(),
  uptime_percentage: Joi.number().min(0).max(100).optional(),
  downtime_minutes: Joi.number().integer().min(0).optional(),
  incidents_count: Joi.number().integer().min(0).optional(),
  notes: Joi.string().optional(),
});

async function getUptimeReportHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;

    const uptimeReport = await prisma.uptimeReport.findUnique({
      where: { id: itemId },
    });

    if (!uptimeReport) {
      return Response.json(
        createApiResponse(null, 'Uptime report not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Transform data to match API response format
    const transformedReport = {
      id: uptimeReport.id,
      property_id: uptimeReport.propertyId,
      service_type: uptimeReport.serviceType,
      date: uptimeReport.date,
      uptime_percentage: uptimeReport.uptimePercentage ? parseFloat(uptimeReport.uptimePercentage.toString()) : null,
      downtime_minutes: uptimeReport.downtimeMinutes,
      incidents_count: uptimeReport.incidentsCount,
      notes: uptimeReport.notes,
      created_at: uptimeReport.createdAt,
      updated_at: uptimeReport.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedReport),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch uptime report');
  }
}

async function updateUptimeReportHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateUptimeReportSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      service_type, 
      date, 
      uptime_percentage, 
      downtime_minutes, 
      incidents_count, 
      notes 
    } = validation.data;

    // Check if uptime report exists
    const existingReport = await prisma.uptimeReport.findUnique({
      where: { id: itemId },
    });

    if (!existingReport) {
      return Response.json(
        createApiResponse(null, 'Uptime report not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Update uptime report
    const updatedReport = await prisma.uptimeReport.update({
      where: { id: itemId },
      data: {
        ...(service_type && { serviceType: service_type }),
        ...(date && { date: new Date(date) }),
        ...(uptime_percentage !== undefined && { uptimePercentage: uptime_percentage }),
        ...(downtime_minutes !== undefined && { downtimeMinutes: downtime_minutes }),
        ...(incidents_count !== undefined && { incidentsCount: incidents_count }),
        ...(notes !== undefined && { notes }),
      },
    });

    // Transform data to match API response format
    const transformedReport = {
      id: updatedReport.id,
      property_id: updatedReport.propertyId,
      service_type: updatedReport.serviceType,
      date: updatedReport.date,
      uptime_percentage: updatedReport.uptimePercentage ? parseFloat(updatedReport.uptimePercentage.toString()) : null,
      downtime_minutes: updatedReport.downtimeMinutes,
      incidents_count: updatedReport.incidentsCount,
      notes: updatedReport.notes,
      created_at: updatedReport.createdAt,
      updated_at: updatedReport.updatedAt,
    };

    return Response.json(
      createApiResponse({
        message: 'Uptime report updated successfully',
        report: transformedReport,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update uptime report');
  }
}

async function deleteUptimeReportHandler(
  request: NextRequest, 
  context: { params: { itemId: string } }, 
  currentUser: any
) {
  try {
    const { itemId } = context.params;

    // Check if uptime report exists
    const existingReport = await prisma.uptimeReport.findUnique({
      where: { id: itemId },
    });

    if (!existingReport) {
      return Response.json(
        createApiResponse(null, 'Uptime report not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Delete uptime report
    await prisma.uptimeReport.delete({
      where: { id: itemId },
    });

    return Response.json(
      createApiResponse({
        message: 'Uptime report deleted successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete uptime report');
  }
}

export const GET = requireAuth(getUptimeReportHandler);
export const PUT = requireAuth(updateUptimeReportHandler);
export const DELETE = requireAuth(deleteUptimeReportHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
