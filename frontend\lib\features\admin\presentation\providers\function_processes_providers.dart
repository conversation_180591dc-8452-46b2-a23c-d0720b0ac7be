import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/function_process.dart';

// Mock data provider for now - replace with real API integration later
final functionProcessesProvider = FutureProvider<List<FunctionProcess>>((ref) async {
  // Mock data for demonstration
  await Future.delayed(const Duration(milliseconds: 500));
  
  return [
    FunctionProcess(
      id: '1',
      name: 'Daily Backup Process',
      description: 'Automated daily backup of all property data',
      category: 'backup',
      priority: 'high',
      status: 'active',
      isAutomated: true,
      executionFrequency: 'daily',
      lastExecuted: DateTime.now().subtract(const Duration(hours: 2)),
      nextExecution: DateTime.now().add(const Duration(hours: 22)),
      successRate: 0.98,
      averageDuration: 1800, // 30 minutes
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    FunctionProcess(
      id: '2',
      name: 'Security Monitoring',
      description: 'Continuous monitoring of security systems',
      category: 'security',
      priority: 'critical',
      status: 'active',
      isAutomated: true,
      executionFrequency: 'hourly',
      lastExecuted: DateTime.now().subtract(const Duration(minutes: 15)),
      nextExecution: DateTime.now().add(const Duration(minutes: 45)),
      successRate: 0.99,
      averageDuration: 300, // 5 minutes
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now().subtract(const Duration(minutes: 15)),
    ),
    FunctionProcess(
      id: '3',
      name: 'Maintenance Report Generation',
      description: 'Weekly generation of maintenance reports',
      category: 'reporting',
      priority: 'medium',
      status: 'active',
      isAutomated: true,
      executionFrequency: 'weekly',
      lastExecuted: DateTime.now().subtract(const Duration(days: 2)),
      nextExecution: DateTime.now().add(const Duration(days: 5)),
      successRate: 0.95,
      averageDuration: 900, // 15 minutes
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    FunctionProcess(
      id: '4',
      name: 'System Cleanup',
      description: 'Monthly cleanup of temporary files and logs',
      category: 'cleanup',
      priority: 'low',
      status: 'inactive',
      isAutomated: false,
      executionFrequency: 'monthly',
      lastExecuted: DateTime.now().subtract(const Duration(days: 15)),
      nextExecution: DateTime.now().add(const Duration(days: 15)),
      successRate: 0.85,
      averageDuration: 2700, // 45 minutes
      createdAt: DateTime.now().subtract(const Duration(days: 120)),
      updatedAt: DateTime.now().subtract(const Duration(days: 15)),
    ),
  ];
});

// State Notifier for CRUD operations
final functionProcessesNotifierProvider = StateNotifierProvider<FunctionProcessesNotifier, FunctionProcessesState>((ref) {
  return FunctionProcessesNotifier();
});

class FunctionProcessesState {
  final List<FunctionProcess> processes;
  final bool isLoading;
  final String? error;

  const FunctionProcessesState({
    this.processes = const [],
    this.isLoading = false,
    this.error,
  });

  FunctionProcessesState copyWith({
    List<FunctionProcess>? processes,
    bool? isLoading,
    String? error,
  }) {
    return FunctionProcessesState(
      processes: processes ?? this.processes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class FunctionProcessesNotifier extends StateNotifier<FunctionProcessesState> {
  FunctionProcessesNotifier() : super(const FunctionProcessesState());

  Future<void> addFunctionProcess(CreateFunctionProcessRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Mock implementation - replace with real API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      final newProcess = FunctionProcess(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: request.name,
        description: request.description,
        category: request.category,
        priority: request.priority,
        status: request.status,
        isAutomated: request.isAutomated,
        executionFrequency: request.executionFrequency,
        createdAt: DateTime.now(),
      );
      
      final updatedProcesses = [...state.processes, newProcess];
      state = state.copyWith(processes: updatedProcesses, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> updateFunctionProcess(String processId, UpdateFunctionProcessRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Mock implementation - replace with real API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      final updatedProcesses = state.processes.map((process) {
        if (process.id == processId) {
          return process.copyWith(
            name: request.name ?? process.name,
            description: request.description ?? process.description,
            category: request.category ?? process.category,
            priority: request.priority ?? process.priority,
            status: request.status ?? process.status,
            isAutomated: request.isAutomated ?? process.isAutomated,
            executionFrequency: request.executionFrequency ?? process.executionFrequency,
            updatedAt: DateTime.now(),
          );
        }
        return process;
      }).toList();
      
      state = state.copyWith(processes: updatedProcesses, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> deleteFunctionProcess(String processId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Mock implementation - replace with real API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      final updatedProcesses = state.processes.where((process) => process.id != processId).toList();
      state = state.copyWith(processes: updatedProcesses, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> toggleProcessStatus(String processId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Mock implementation - replace with real API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      final updatedProcesses = state.processes.map((process) {
        if (process.id == processId) {
          final newStatus = process.isActive ? 'inactive' : 'active';
          return process.copyWith(
            status: newStatus,
            updatedAt: DateTime.now(),
          );
        }
        return process;
      }).toList();
      
      state = state.copyWith(processes: updatedProcesses, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Request Models
class CreateFunctionProcessRequest {
  final String name;
  final String? description;
  final String category;
  final String priority;
  final String status;
  final bool isAutomated;
  final String? executionFrequency;

  const CreateFunctionProcessRequest({
    required this.name,
    this.description,
    required this.category,
    required this.priority,
    required this.status,
    required this.isAutomated,
    this.executionFrequency,
  });
}

class UpdateFunctionProcessRequest {
  final String? name;
  final String? description;
  final String? category;
  final String? priority;
  final String? status;
  final bool? isAutomated;
  final String? executionFrequency;

  const UpdateFunctionProcessRequest({
    this.name,
    this.description,
    this.category,
    this.priority,
    this.status,
    this.isAutomated,
    this.executionFrequency,
  });
}
