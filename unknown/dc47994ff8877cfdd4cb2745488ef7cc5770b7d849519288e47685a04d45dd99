// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ott_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OttService _$OttServiceFromJson(Map<String, dynamic> json) => OttService(
      id: json['id'] as String,
      propertyId: json['property_id'] as String,
      serviceName: json['service_name'] as String,
      provider: json['provider'] as String,
      description: json['description'] as String?,
      monthlyCost: (json['monthly_cost'] as num?)?.toDouble(),
      subscriptionType: json['subscription_type'] as String?,
      subscriptionStartDate: json['subscription_start_date'] == null
          ? null
          : DateTime.parse(json['subscription_start_date'] as String),
      subscriptionEndDate: json['subscription_end_date'] == null
          ? null
          : DateTime.parse(json['subscription_end_date'] as String),
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$OttServiceToJson(OttService instance) =>
    <String, dynamic>{
      'id': instance.id,
      'property_id': instance.propertyId,
      'service_name': instance.serviceName,
      'provider': instance.provider,
      'description': instance.description,
      'monthly_cost': instance.monthlyCost,
      'subscription_type': instance.subscriptionType,
      'subscription_start_date':
          instance.subscriptionStartDate?.toIso8601String(),
      'subscription_end_date': instance.subscriptionEndDate?.toIso8601String(),
      'status': instance.status,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
