import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'uptime_report.g.dart';

@JsonSerializable()
class UptimeReport {
  final String id;
  @J<PERSON><PERSON><PERSON>(name: 'property_id')
  final String propertyId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'service_type')
  final String serviceType;
  final DateTime date;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'uptime_percentage')
  final double? uptimePercentage;
  @<PERSON>son<PERSON>ey(name: 'downtime_minutes')
  final int? downtimeMinutes;
  @<PERSON>son<PERSON><PERSON>(name: 'incidents_count')
  final int? incidentsCount;
  final String? notes;
  @Json<PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime? updatedAt;

  const UptimeReport({
    required this.id,
    required this.propertyId,
    required this.serviceType,
    required this.date,
    this.uptimePercentage,
    this.downtimeMinutes,
    this.incidentsCount,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  factory UptimeReport.from<PERSON>son(Map<String, dynamic> json) => _$UptimeReportFromJson(json);
  Map<String, dynamic> toJson() => _$UptimeReportToJson(this);

  UptimeReport copyWith({
    String? id,
    String? propertyId,
    String? serviceType,
    DateTime? date,
    double? uptimePercentage,
    int? downtimeMinutes,
    int? incidentsCount,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UptimeReport(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      serviceType: serviceType ?? this.serviceType,
      date: date ?? this.date,
      uptimePercentage: uptimePercentage ?? this.uptimePercentage,
      downtimeMinutes: downtimeMinutes ?? this.downtimeMinutes,
      incidentsCount: incidentsCount ?? this.incidentsCount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UptimeReport &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UptimeReport{id: $id, serviceType: $serviceType, date: $date, uptimePercentage: $uptimePercentage}';
  }

  // Business logic methods
  String get serviceTypeDisplayName {
    switch (serviceType.toLowerCase()) {
      case 'electricity':
        return 'Electricity';
      case 'internet':
        return 'Internet';
      case 'water':
        return 'Water';
      case 'generator':
        return 'Generator';
      case 'hvac':
        return 'HVAC';
      case 'security':
        return 'Security System';
      default:
        return serviceType;
    }
  }

  IconData get serviceIcon {
    switch (serviceType.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'internet':
        return Icons.wifi;
      case 'water':
        return Icons.water_drop;
      case 'generator':
        return Icons.power;
      case 'hvac':
        return Icons.air;
      case 'security':
        return Icons.security;
      default:
        return Icons.miscellaneous_services;
    }
  }

  Color get serviceColor {
    switch (serviceType.toLowerCase()) {
      case 'electricity':
        return Colors.amber;
      case 'internet':
        return Colors.blue;
      case 'water':
        return Colors.cyan;
      case 'generator':
        return Colors.orange;
      case 'hvac':
        return Colors.green;
      case 'security':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String get uptimeDisplayText {
    if (uptimePercentage == null) return 'Not specified';
    return '${uptimePercentage!.toStringAsFixed(1)}%';
  }

  String get downtimeDisplayText {
    if (downtimeMinutes == null) return 'Not specified';
    
    if (downtimeMinutes! < 60) {
      return '${downtimeMinutes!}m';
    } else {
      final hours = downtimeMinutes! ~/ 60;
      final minutes = downtimeMinutes! % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}m';
      }
    }
  }

  String get incidentsDisplayText {
    if (incidentsCount == null) return 'Not specified';
    return incidentsCount == 1 ? '1 incident' : '$incidentsCount incidents';
  }

  Color get uptimeColor {
    if (uptimePercentage == null) return Colors.grey;
    
    if (uptimePercentage! >= 99.0) return Colors.green;
    if (uptimePercentage! >= 95.0) return Colors.lightGreen;
    if (uptimePercentage! >= 90.0) return Colors.orange;
    if (uptimePercentage! >= 80.0) return Colors.deepOrange;
    return Colors.red;
  }

  String get uptimeQualityText {
    if (uptimePercentage == null) return 'Unknown';
    
    if (uptimePercentage! >= 99.9) return 'Excellent';
    if (uptimePercentage! >= 99.0) return 'Very Good';
    if (uptimePercentage! >= 95.0) return 'Good';
    if (uptimePercentage! >= 90.0) return 'Fair';
    if (uptimePercentage! >= 80.0) return 'Poor';
    return 'Very Poor';
  }

  String get dateFormatted => _formatDate(date);
  String get createdAtFormatted => _formatDateTime(createdAt);

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${_formatTime(dateTime)}';
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasUptimeData => uptimePercentage != null;
  bool get hasDowntimeData => downtimeMinutes != null;
  bool get hasIncidentData => incidentsCount != null;

  // Time-based properties
  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  bool get isThisWeek {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));
    return date.isAfter(weekAgo);
  }

  bool get isThisMonth {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  String get timeAgoText {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Today';
    }
  }

  // Calculated properties
  double? get calculatedUptimeFromDowntime {
    if (downtimeMinutes == null) return null;
    
    const totalMinutesInDay = 24 * 60; // 1440 minutes
    final uptimeMinutes = totalMinutesInDay - downtimeMinutes!;
    return (uptimeMinutes / totalMinutesInDay) * 100;
  }

  int? get calculatedDowntimeFromUptime {
    if (uptimePercentage == null) return null;
    
    const totalMinutesInDay = 24 * 60; // 1440 minutes
    final downtimePercentage = 100 - uptimePercentage!;
    return ((downtimePercentage / 100) * totalMinutesInDay).round();
  }

  // Validation methods
  bool get isValid => serviceType.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    
    if (serviceType.isEmpty) errors.add('Service type is required');
    
    if (uptimePercentage != null) {
      if (uptimePercentage! < 0 || uptimePercentage! > 100) {
        errors.add('Uptime percentage must be between 0 and 100');
      }
    }
    
    if (downtimeMinutes != null && downtimeMinutes! < 0) {
      errors.add('Downtime minutes cannot be negative');
    }
    
    if (incidentsCount != null && incidentsCount! < 0) {
      errors.add('Incidents count cannot be negative');
    }
    
    // Check consistency between uptime percentage and downtime minutes
    if (uptimePercentage != null && downtimeMinutes != null) {
      final calculatedUptime = calculatedUptimeFromDowntime;
      if (calculatedUptime != null) {
        final difference = (uptimePercentage! - calculatedUptime).abs();
        if (difference > 1.0) { // Allow 1% tolerance
          errors.add('Uptime percentage and downtime minutes are inconsistent');
        }
      }
    }
    
    return errors;
  }

  // Performance scoring
  double get performanceScore {
    if (uptimePercentage == null) return 0.5; // Neutral score for missing data
    
    // Base score from uptime percentage
    double score = uptimePercentage! / 100;
    
    // Penalty for incidents
    if (incidentsCount != null && incidentsCount! > 0) {
      final incidentPenalty = incidentsCount! * 0.05; // 5% penalty per incident
      score = (score - incidentPenalty).clamp(0.0, 1.0);
    }
    
    return score;
  }

  String get performanceGrade {
    final score = performanceScore;
    
    if (score >= 0.95) return 'A+';
    if (score >= 0.90) return 'A';
    if (score >= 0.85) return 'B+';
    if (score >= 0.80) return 'B';
    if (score >= 0.75) return 'C+';
    if (score >= 0.70) return 'C';
    if (score >= 0.60) return 'D';
    return 'F';
  }

  Color get performanceGradeColor {
    final score = performanceScore;
    
    if (score >= 0.90) return Colors.green;
    if (score >= 0.80) return Colors.lightGreen;
    if (score >= 0.70) return Colors.orange;
    if (score >= 0.60) return Colors.deepOrange;
    return Colors.red;
  }

  // Summary text for cards/lists
  String get summaryText {
    final parts = <String>[];
    
    if (hasUptimeData) {
      parts.add('${uptimeDisplayText} uptime');
    }
    
    if (hasDowntimeData) {
      parts.add('${downtimeDisplayText} downtime');
    }
    
    if (hasIncidentData && incidentsCount! > 0) {
      parts.add(incidentsDisplayText);
    }
    
    if (parts.isEmpty) {
      return 'No performance data';
    }
    
    return parts.join(' • ');
  }
}
