import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/diesel_addition.dart';
import '../../data/diesel_addition_api_service.dart' as api;
import '../providers/diesel_additions_providers.dart';

class AddDieselAdditionDialog extends ConsumerStatefulWidget {
  final String propertyId;
  final DieselAddition? addition; // For editing
  final VoidCallback? onAdditionAdded;

  const AddDieselAdditionDialog({
    super.key,
    required this.propertyId,
    this.addition,
    this.onAdditionAdded,
  });

  @override
  ConsumerState<AddDieselAdditionDialog> createState() => _AddDieselAdditionDialogState();
}

class _AddDieselAdditionDialogState extends ConsumerState<AddDieselAdditionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _costPerLiterController = TextEditingController();
  final _totalCostController = TextEditingController();
  final _supplierController = TextEditingController();
  final _receiptNumberController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  bool _autoCalculateTotal = true;

  @override
  void initState() {
    super.initState();
    if (widget.addition != null) {
      _populateFields();
    }

    // Add listeners for auto-calculation
    _quantityController.addListener(_calculateTotal);
    _costPerLiterController.addListener(_calculateTotal);
  }

  void _populateFields() {
    final addition = widget.addition!;
    _quantityController.text = addition.quantity.toString();
    _costPerLiterController.text = addition.costPerLiter?.toString() ?? '';
    _totalCostController.text = addition.totalCost?.toString() ?? '';
    _supplierController.text = addition.supplier ?? '';
    _receiptNumberController.text = addition.receiptNumber ?? '';
    _notesController.text = addition.notes ?? '';
  }

  void _calculateTotal() {
    if (!_autoCalculateTotal) return;

    final quantity = double.tryParse(_quantityController.text);
    final costPerLiter = double.tryParse(_costPerLiterController.text);

    if (quantity != null && costPerLiter != null) {
      final total = quantity * costPerLiter;
      _totalCostController.text = total.toStringAsFixed(2);
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _costPerLiterController.dispose();
    _totalCostController.dispose();
    _supplierController.dispose();
    _receiptNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.addition != null;

    return AlertDialog(
      title: Text(isEditing ? 'Edit Diesel Addition' : 'Add Diesel Addition'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Quantity
                TextFormField(
                  controller: _quantityController,
                  decoration: const InputDecoration(
                    labelText: 'Quantity (Liters) *',
                    hintText: 'e.g., 500.0',
                    suffixText: 'L',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Quantity is required';
                    }
                    final quantity = double.tryParse(value);
                    if (quantity == null || quantity <= 0) {
                      return 'Please enter a valid quantity greater than 0';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Supplier
                TextFormField(
                  controller: _supplierController,
                  decoration: const InputDecoration(
                    labelText: 'Supplier',
                    hintText: 'e.g., Shell, BP, Local Station',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Cost per Liter and Total Cost Row
                Row(
                  children: [
                    // Cost per Liter
                    Expanded(
                      child: TextFormField(
                        controller: _costPerLiterController,
                        decoration: const InputDecoration(
                          labelText: 'Cost per Liter',
                          hintText: '0.00',
                          prefixText: '\$ ',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final cost = double.tryParse(value);
                            if (cost == null || cost < 0) {
                              return 'Invalid cost';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),

                    // Total Cost
                    Expanded(
                      child: TextFormField(
                        controller: _totalCostController,
                        decoration: InputDecoration(
                          labelText: 'Total Cost',
                          hintText: '0.00',
                          prefixText: '\$ ',
                          border: const OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _autoCalculateTotal ? Icons.calculate : Icons.edit,
                              size: 20,
                            ),
                            onPressed: () {
                              setState(() {
                                _autoCalculateTotal = !_autoCalculateTotal;
                              });
                              if (_autoCalculateTotal) {
                                _calculateTotal();
                              }
                            },
                            tooltip: _autoCalculateTotal ? 'Manual entry' : 'Auto calculate',
                          ),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        readOnly: _autoCalculateTotal,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final cost = double.tryParse(value);
                            if (cost == null || cost < 0) {
                              return 'Invalid cost';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Receipt Number
                TextFormField(
                  controller: _receiptNumberController,
                  decoration: const InputDecoration(
                    labelText: 'Receipt Number',
                    hintText: 'Optional receipt/invoice number',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Notes
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    hintText: 'Optional notes or comments',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),

                // Auto-calculate info
                if (_autoCalculateTotal) ...[
                  const SizedBox(height: AppConstants.smallPadding),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info,
                          size: 16,
                          color: Colors.blue[700],
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Total cost is automatically calculated from quantity × cost per liter',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveAddition,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  Future<void> _saveAddition() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final quantity = double.parse(_quantityController.text);
      final costPerLiter = _costPerLiterController.text.isNotEmpty
          ? double.tryParse(_costPerLiterController.text)
          : null;
      final totalCost = _totalCostController.text.isNotEmpty
          ? double.tryParse(_totalCostController.text)
          : null;

      // Validate cost consistency if both are provided
      if (costPerLiter != null && totalCost != null) {
        final calculatedTotal = quantity * costPerLiter;
        final difference = (calculatedTotal - totalCost).abs();
        if (difference > 0.01) {
          AppUtils.showErrorSnackBar(
            context,
            'Total cost does not match quantity × cost per liter',
          );
          return;
        }
      }

      if (widget.addition != null) {
        // Update existing addition
        final request = api.UpdateDieselAdditionRequest(
          quantityLiters: quantity,
          costPerLiter: costPerLiter,
          supplierName: _supplierController.text.trim().isEmpty
              ? null
              : _supplierController.text.trim(),
          invoiceNumber: _receiptNumberController.text.trim().isEmpty
              ? null
              : _receiptNumberController.text.trim(),
          deliveryDate: DateTime.now(), // Use current date for now
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
        );

        await ref.read(dieselAdditionsNotifierProvider(widget.propertyId).notifier)
            .updateDieselAddition(widget.addition!.id, request);
      } else {
        // Create new addition
        final request = api.CreateDieselAdditionRequest(
          quantityLiters: quantity,
          costPerLiter: costPerLiter ?? 0.0,
          supplierName: _supplierController.text.trim().isEmpty
              ? null
              : _supplierController.text.trim(),
          invoiceNumber: _receiptNumberController.text.trim().isEmpty
              ? null
              : _receiptNumberController.text.trim(),
          deliveryDate: DateTime.now(), // Use current date for now
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
        );

        await ref.read(dieselAdditionsNotifierProvider(widget.propertyId).notifier)
            .addDieselAddition(request);
      }

      if (mounted) {
        Navigator.of(context).pop();
        widget.onAdditionAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to save diesel addition: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
