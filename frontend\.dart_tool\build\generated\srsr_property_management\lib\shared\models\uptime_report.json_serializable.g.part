// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UptimeReport _$UptimeReportFromJson(Map<String, dynamic> json) => UptimeReport(
      id: json['id'] as String,
      propertyId: json['property_id'] as String,
      serviceType: json['service_type'] as String,
      date: DateTime.parse(json['date'] as String),
      uptimePercentage: (json['uptime_percentage'] as num?)?.toDouble(),
      downtimeMinutes: (json['downtime_minutes'] as num?)?.toInt(),
      incidentsCount: (json['incidents_count'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$UptimeReportToJson(UptimeReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'property_id': instance.propertyId,
      'service_type': instance.serviceType,
      'date': instance.date.toIso8601String(),
      'uptime_percentage': instance.uptimePercentage,
      'downtime_minutes': instance.downtimeMinutes,
      'incidents_count': instance.incidentsCount,
      'notes': instance.notes,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };
