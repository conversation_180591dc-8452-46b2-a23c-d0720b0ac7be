import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/uptime_report.dart' as models;
import '../../data/uptime_report_api_service.dart' as api;
import '../../../../core/network/dio_client.dart';

// API Service Provider
final uptimeReportApiProvider = Provider<api.UptimeReportApiService>((ref) {
  final dio = ref.read(dioClientProvider);
  return api.UptimeReportApiService(dio);
});

// Repository Provider
final uptimeReportRepositoryProvider = Provider<UptimeReportRepository>((ref) {
  final apiService = ref.read(uptimeReportApiProvider);
  return UptimeReportRepositoryImpl(apiService);
});

// Uptime Reports by Property Provider
final uptimeReportsByPropertyProvider = FutureProvider.family<List<models.UptimeReport>, String>((ref, propertyId) async {
  final repository = ref.read(uptimeReportRepositoryProvider);
  return repository.getUptimeReports(propertyId);
});

// Uptime Reports Notifier for CRUD operations
final uptimeReportsNotifierProvider = StateNotifierProvider.family<UptimeReportsNotifier, UptimeReportsState, String>(
  (ref, propertyId) {
    final repository = ref.read(uptimeReportRepositoryProvider);
    return UptimeReportsNotifier(repository, propertyId);
  },
);

// State class for Uptime Reports
class UptimeReportsState {
  final List<models.UptimeReport> reports;
  final bool isLoading;
  final String? error;

  const UptimeReportsState({
    this.reports = const [],
    this.isLoading = false,
    this.error,
  });

  UptimeReportsState copyWith({
    List<models.UptimeReport>? reports,
    bool? isLoading,
    String? error,
  }) {
    return UptimeReportsState(
      reports: reports ?? this.reports,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// State Notifier for Uptime Reports CRUD operations
class UptimeReportsNotifier extends StateNotifier<UptimeReportsState> {
  final UptimeReportRepository _repository;
  final String _propertyId;

  UptimeReportsNotifier(this._repository, this._propertyId) : super(const UptimeReportsState()) {
    loadReports();
  }

  Future<void> loadReports() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final reports = await _repository.getUptimeReports(_propertyId);
      state = state.copyWith(reports: reports, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> addUptimeReport(api.CreateUptimeReportRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final newReport = await _repository.createUptimeReport(_propertyId, request);
      final updatedReports = [...state.reports, newReport];
      state = state.copyWith(reports: updatedReports, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> updateUptimeReport(String reportId, api.UpdateUptimeReportRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedReport = await _repository.updateUptimeReport(reportId, request);
      final updatedReports = state.reports.map((report) {
        return report.id == reportId ? updatedReport : report;
      }).toList();
      state = state.copyWith(reports: updatedReports, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> deleteUptimeReport(String reportId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _repository.deleteUptimeReport(reportId);
      final updatedReports = state.reports.where((report) => report.id != reportId).toList();
      state = state.copyWith(reports: updatedReports, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Repository Interface
abstract class UptimeReportRepository {
  Future<List<models.UptimeReport>> getUptimeReports(String propertyId);
  Future<models.UptimeReport> createUptimeReport(String propertyId, api.CreateUptimeReportRequest request);
  Future<models.UptimeReport> updateUptimeReport(String reportId, api.UpdateUptimeReportRequest request);
  Future<void> deleteUptimeReport(String reportId);
}

// Repository Implementation
class UptimeReportRepositoryImpl implements UptimeReportRepository {
  final api.UptimeReportApiService _apiService;

  UptimeReportRepositoryImpl(this._apiService);

  @override
  Future<List<models.UptimeReport>> getUptimeReports(String propertyId) async {
    try {
      final response = await _apiService.getUptimeReports(propertyId);
      if (response.success && response.data != null) {
        // Convert API models to domain models
        return response.data!.map((apiModel) => models.UptimeReport(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          serviceType: apiModel.serviceType,
          date: DateTime.parse(apiModel.date),
          uptimePercentage: apiModel.uptimePercentage,
          downtimeMinutes: apiModel.downtimeMinutes,
          incidentsCount: apiModel.incidentsCount,
          notes: apiModel.notes,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        )).toList();
      } else {
        throw Exception(response.message ?? 'Failed to fetch uptime reports');
      }
    } catch (e) {
      throw Exception('Failed to fetch uptime reports: $e');
    }
  }

  @override
  Future<models.UptimeReport> createUptimeReport(String propertyId, api.CreateUptimeReportRequest request) async {
    try {
      final response = await _apiService.createUptimeReport(propertyId, request);
      if (response.success && response.data != null) {
        final apiModel = response.data!;
        return models.UptimeReport(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          serviceType: apiModel.serviceType,
          date: DateTime.parse(apiModel.date),
          uptimePercentage: apiModel.uptimePercentage,
          downtimeMinutes: apiModel.downtimeMinutes,
          incidentsCount: apiModel.incidentsCount,
          notes: apiModel.notes,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        );
      } else {
        throw Exception(response.message ?? 'Failed to create uptime report');
      }
    } catch (e) {
      throw Exception('Failed to create uptime report: $e');
    }
  }

  @override
  Future<models.UptimeReport> updateUptimeReport(String reportId, api.UpdateUptimeReportRequest request) async {
    try {
      final response = await _apiService.updateUptimeReport(reportId, request);
      if (response.success && response.data != null) {
        final apiModel = response.data!;
        return models.UptimeReport(
          id: apiModel.id,
          propertyId: apiModel.propertyId,
          serviceType: apiModel.serviceType,
          date: DateTime.parse(apiModel.date),
          uptimePercentage: apiModel.uptimePercentage,
          downtimeMinutes: apiModel.downtimeMinutes,
          incidentsCount: apiModel.incidentsCount,
          notes: apiModel.notes,
          createdAt: apiModel.createdAt,
          updatedAt: apiModel.updatedAt,
        );
      } else {
        throw Exception(response.message ?? 'Failed to update uptime report');
      }
    } catch (e) {
      throw Exception('Failed to update uptime report: $e');
    }
  }

  @override
  Future<void> deleteUptimeReport(String reportId) async {
    try {
      final response = await _apiService.deleteUptimeReport(reportId);
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to delete uptime report');
      }
    } catch (e) {
      throw Exception('Failed to delete uptime report: $e');
    }
  }
}
