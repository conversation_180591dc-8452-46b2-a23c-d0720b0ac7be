import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/uptime_report.dart';
import '../../../../shared/models/api_response.dart';
import '../../data/uptime_report_api_service.dart';
import '../../../../core/network/dio_client.dart';

// API Service Provider
final uptimeReportApiProvider = Provider<UptimeReportApiService>((ref) {
  final dio = ref.read(dioClientProvider);
  return UptimeReportApiService(dio);
});

// Repository Provider
final uptimeReportRepositoryProvider = Provider<UptimeReportRepository>((ref) {
  final apiService = ref.read(uptimeReportApiProvider);
  return UptimeReportRepositoryImpl(apiService);
});

// Uptime Reports by Property Provider
final uptimeReportsByPropertyProvider = FutureProvider.family<List<UptimeReport>, String>((ref, propertyId) async {
  final repository = ref.read(uptimeReportRepositoryProvider);
  return repository.getUptimeReports(propertyId);
});

// Uptime Reports Notifier for CRUD operations
final uptimeReportsNotifierProvider = StateNotifierProvider.family<UptimeReportsNotifier, UptimeReportsState, String>(
  (ref, propertyId) {
    final repository = ref.read(uptimeReportRepositoryProvider);
    return UptimeReportsNotifier(repository, propertyId);
  },
);

// State class for Uptime Reports
class UptimeReportsState {
  final List<UptimeReport> reports;
  final bool isLoading;
  final String? error;

  const UptimeReportsState({
    this.reports = const [],
    this.isLoading = false,
    this.error,
  });

  UptimeReportsState copyWith({
    List<UptimeReport>? reports,
    bool? isLoading,
    String? error,
  }) {
    return UptimeReportsState(
      reports: reports ?? this.reports,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// State Notifier for Uptime Reports CRUD operations
class UptimeReportsNotifier extends StateNotifier<UptimeReportsState> {
  final UptimeReportRepository _repository;
  final String _propertyId;

  UptimeReportsNotifier(this._repository, this._propertyId) : super(const UptimeReportsState()) {
    loadReports();
  }

  Future<void> loadReports() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final reports = await _repository.getUptimeReports(_propertyId);
      state = state.copyWith(reports: reports, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }

  Future<void> addUptimeReport(CreateUptimeReportRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final newReport = await _repository.createUptimeReport(_propertyId, request);
      final updatedReports = [...state.reports, newReport];
      state = state.copyWith(reports: updatedReports, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> updateUptimeReport(String reportId, UpdateUptimeReportRequest request) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedReport = await _repository.updateUptimeReport(reportId, request);
      final updatedReports = state.reports.map((report) {
        return report.id == reportId ? updatedReport : report;
      }).toList();
      state = state.copyWith(reports: updatedReports, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  Future<void> deleteUptimeReport(String reportId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _repository.deleteUptimeReport(reportId);
      final updatedReports = state.reports.where((report) => report.id != reportId).toList();
      state = state.copyWith(reports: updatedReports, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Repository Interface
abstract class UptimeReportRepository {
  Future<List<UptimeReport>> getUptimeReports(String propertyId);
  Future<UptimeReport> createUptimeReport(String propertyId, CreateUptimeReportRequest request);
  Future<UptimeReport> updateUptimeReport(String reportId, UpdateUptimeReportRequest request);
  Future<void> deleteUptimeReport(String reportId);
}

// Repository Implementation
class UptimeReportRepositoryImpl implements UptimeReportRepository {
  final UptimeReportApiService _apiService;

  UptimeReportRepositoryImpl(this._apiService);

  @override
  Future<List<UptimeReport>> getUptimeReports(String propertyId) async {
    try {
      final response = await _apiService.getUptimeReports(propertyId);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to fetch uptime reports');
      }
    } catch (e) {
      throw Exception('Failed to fetch uptime reports: $e');
    }
  }

  @override
  Future<UptimeReport> createUptimeReport(String propertyId, CreateUptimeReportRequest request) async {
    try {
      final response = await _apiService.createUptimeReport(propertyId, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to create uptime report');
      }
    } catch (e) {
      throw Exception('Failed to create uptime report: $e');
    }
  }

  @override
  Future<UptimeReport> updateUptimeReport(String reportId, UpdateUptimeReportRequest request) async {
    try {
      final response = await _apiService.updateUptimeReport(reportId, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to update uptime report');
      }
    } catch (e) {
      throw Exception('Failed to update uptime report: $e');
    }
  }

  @override
  Future<void> deleteUptimeReport(String reportId) async {
    try {
      final response = await _apiService.deleteUptimeReport(reportId);
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to delete uptime report');
      }
    } catch (e) {
      throw Exception('Failed to delete uptime report: $e');
    }
  }
}

// Request Models
class CreateUptimeReportRequest {
  final String serviceType;
  final DateTime date;
  final double? uptimePercentage;
  final int? downtimeMinutes;
  final int? incidentsCount;
  final String? notes;

  const CreateUptimeReportRequest({
    required this.serviceType,
    required this.date,
    this.uptimePercentage,
    this.downtimeMinutes,
    this.incidentsCount,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'service_type': serviceType,
      'date': date.toIso8601String(),
      'uptime_percentage': uptimePercentage,
      'downtime_minutes': downtimeMinutes,
      'incidents_count': incidentsCount,
      'notes': notes,
    };
  }
}

class UpdateUptimeReportRequest {
  final String? serviceType;
  final DateTime? date;
  final double? uptimePercentage;
  final int? downtimeMinutes;
  final int? incidentsCount;
  final String? notes;

  const UpdateUptimeReportRequest({
    this.serviceType,
    this.date,
    this.uptimePercentage,
    this.downtimeMinutes,
    this.incidentsCount,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (serviceType != null) json['service_type'] = serviceType;
    if (date != null) json['date'] = date!.toIso8601String();
    if (uptimePercentage != null) json['uptime_percentage'] = uptimePercentage;
    if (downtimeMinutes != null) json['downtime_minutes'] = downtimeMinutes;
    if (incidentsCount != null) json['incidents_count'] = incidentsCount;
    if (notes != null) json['notes'] = notes;
    return json;
  }
}
