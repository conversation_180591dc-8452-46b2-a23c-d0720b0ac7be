import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/providers/auth_providers.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/auth/presentation/screens/register_screen.dart';
import '../../features/auth/presentation/screens/profile_screen.dart';
import '../../features/dashboard/presentation/screens/dashboard_screen.dart';
import '../../features/dashboard/presentation/screens/debug_dashboard_screen.dart';
import '../../features/properties/presentation/screens/properties_screen.dart';
import '../../features/properties/presentation/screens/property_details_screen.dart';
import '../../features/properties/presentation/screens/ott_services_screen.dart';
import '../../features/properties/presentation/screens/diesel_additions_screen.dart';
import '../../features/properties/presentation/screens/uptime_reports_screen.dart';
import '../../features/maintenance/presentation/screens/maintenance_screen.dart';
import '../../features/maintenance/presentation/screens/enhanced_maintenance_screen.dart';
import '../../features/attendance/presentation/screens/attendance_screen.dart';
import '../../features/attendance/presentation/screens/enhanced_attendance_screen.dart';
import '../../features/fuel/presentation/screens/fuel_monitoring_screen.dart';
import '../../features/admin/presentation/screens/admin_dashboard_screen.dart';
import '../../features/admin/presentation/screens/function_processes_screen.dart';
import '../../features/admin/presentation/screens/user_management_screen.dart';
import '../../features/admin/presentation/screens/threshold_config_screen.dart';
import '../../features/admin/presentation/screens/role_management_screen.dart';
import '../../features/admin/presentation/screens/screen_management_screen.dart';
import '../../features/admin/presentation/screens/widget_management_screen.dart';
import '../../features/admin/presentation/screens/permission_config_screen.dart';
import '../../features/settings/presentation/screens/settings_screen.dart';
import '../../features/reports/presentation/screens/reports_screen.dart';
import '../../features/security/presentation/screens/security_screen.dart';


final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);

  return GoRouter(
    initialLocation: authState.isAuthenticated ? '/dashboard' : '/login',
    redirect: (context, state) {
      final isAuthenticated = authState.isAuthenticated;
      final isLoggingIn = state.matchedLocation == '/login' || state.matchedLocation == '/register';

      // If not authenticated and not on login/register page, redirect to login
      if (!isAuthenticated && !isLoggingIn) {
        return '/login';
      }

      // If authenticated and on login/register page, redirect to dashboard
      if (isAuthenticated && isLoggingIn) {
        return '/dashboard';
      }

      return null;
    },
    routes: [
      // Auth Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),

      // Profile Route
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),

      // Settings Route
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),

      // Reports Route
      GoRoute(
        path: '/reports',
        name: 'reports',
        builder: (context, state) => const ReportsScreen(),
      ),

      // Security Route
      GoRoute(
        path: '/security',
        name: 'security',
        builder: (context, state) => const SecurityScreen(),
      ),

      // Main App Routes
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),

      // Debug Dashboard (bypasses permissions)
      GoRoute(
        path: '/debug-dashboard',
        name: 'debug-dashboard',
        builder: (context, state) => const DebugDashboardScreen(),
      ),

      // Properties Routes
      GoRoute(
        path: '/properties',
        name: 'properties',
        builder: (context, state) => const PropertiesScreen(),
        routes: [
          GoRoute(
            path: '/:id',
            name: 'property-details',
            builder: (context, state) {
              final propertyId = state.pathParameters['id']!;
              return PropertyDetailsScreen(propertyId: propertyId);
            },
            routes: [
              GoRoute(
                path: '/ott-services',
                name: 'property-ott-services',
                builder: (context, state) {
                  final propertyId = state.pathParameters['id']!;
                  return OttServicesScreen(propertyId: propertyId);
                },
              ),
              GoRoute(
                path: '/diesel-additions',
                name: 'property-diesel-additions',
                builder: (context, state) {
                  final propertyId = state.pathParameters['id']!;
                  return DieselAdditionsScreen(propertyId: propertyId);
                },
              ),
              GoRoute(
                path: '/uptime-reports',
                name: 'property-uptime-reports',
                builder: (context, state) {
                  final propertyId = state.pathParameters['id']!;
                  return UptimeReportsScreen(propertyId: propertyId);
                },
              ),
            ],
          ),
        ],
      ),

      // Maintenance Routes
      GoRoute(
        path: '/maintenance',
        name: 'maintenance',
        builder: (context, state) => const MaintenanceScreen(),
      ),
      GoRoute(
        path: '/enhanced-maintenance',
        name: 'enhanced-maintenance',
        builder: (context, state) => const EnhancedMaintenanceScreen(),
      ),

      // Attendance Routes
      GoRoute(
        path: '/attendance',
        name: 'attendance',
        builder: (context, state) => const AttendanceScreen(),
      ),
      GoRoute(
        path: '/enhanced-attendance',
        name: 'enhanced-attendance',
        builder: (context, state) => const EnhancedAttendanceScreen(),
      ),

      // Generator Fuel Routes
      GoRoute(
        path: '/fuel',
        name: 'fuel',
        builder: (context, state) => const FuelMonitoringScreen(),
      ),

      // Admin Routes
      GoRoute(
        path: '/admin',
        name: 'admin',
        builder: (context, state) => const AdminDashboardScreen(),
        routes: [
          GoRoute(
            path: '/users',
            name: 'admin-users',
            builder: (context, state) => const UserManagementScreen(),
          ),
          GoRoute(
            path: '/thresholds',
            name: 'admin-thresholds',
            builder: (context, state) => const ThresholdConfigScreen(),
          ),
          GoRoute(
            path: '/roles',
            name: 'admin-roles',
            builder: (context, state) => const RoleManagementScreen(),
          ),
          GoRoute(
            path: '/screens',
            name: 'admin-screens',
            builder: (context, state) => const ScreenManagementScreen(),
          ),
          GoRoute(
            path: '/widgets',
            name: 'admin-widgets',
            builder: (context, state) => const WidgetManagementScreen(),
          ),
          GoRoute(
            path: '/permissions',
            name: 'admin-permissions',
            builder: (context, state) => const PermissionConfigScreen(),
          ),
          GoRoute(
            path: '/function-processes',
            name: 'admin-function-processes',
            builder: (context, state) => const FunctionProcessesScreen(),
          ),
        ],
      ),

      // Profile Routes
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => Scaffold(
          appBar: AppBar(title: const Text('Profile')),
          body: const Center(
            child: Text('Profile Screen - Coming Soon'),
          ),
        ),
      ),

      // Settings Routes
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => Scaffold(
          appBar: AppBar(title: const Text('Settings')),
          body: const Center(
            child: Text('Settings Screen - Coming Soon'),
          ),
        ),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helper methods
class AppRouter {
  static void goToLogin(BuildContext context) {
    context.go('/login');
  }

  static void goToRegister(BuildContext context) {
    context.push('/register');
  }

  static void goToDashboard(BuildContext context) {
    context.go('/dashboard');
  }

  static void goToProperties(BuildContext context) {
    context.push('/properties');
  }

  static void goToMaintenance(BuildContext context) {
    context.push('/maintenance');
  }

  static void goToAttendance(BuildContext context) {
    context.push('/attendance');
  }

  static void goToFuel(BuildContext context) {
    context.push('/fuel');
  }

  // Legacy method for backward compatibility
  static void goToGeneratorFuel(BuildContext context) {
    context.push('/fuel');
  }

  static void goToProfile(BuildContext context) {
    context.push('/profile');
  }

  static void goToSettings(BuildContext context) {
    context.push('/settings');
  }

  static void goToReports(BuildContext context) {
    context.push('/reports');
  }

  static void goToSecurity(BuildContext context) {
    context.push('/security');
  }
}
