import 'package:json_annotation/json_annotation.dart';

part 'property_member.g.dart';

@JsonSerializable()
class PropertyMember {
  final String id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'property_id')
  final String propertyId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String userId;
  final String? role;
  final String? position;
  final String? department;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'hourly_rate')
  final double? hourlyRate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'start_date')
  final DateTime? startDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'end_date')
  final DateTime? endDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;

  const PropertyMember({
    required this.id,
    required this.propertyId,
    required this.userId,
    this.role,
    this.position,
    this.department,
    this.hourlyRate,
    this.startDate,
    this.endDate,
    required this.isActive,
    required this.createdAt,
  });

  factory PropertyMember.fromJson(Map<String, dynamic> json) => _$PropertyMemberFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyMemberToJson(this);

  PropertyMember copyWith({
    String? id,
    String? propertyId,
    String? userId,
    String? role,
    String? position,
    String? department,
    double? hourlyRate,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return PropertyMember(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      position: position ?? this.position,
      department: department ?? this.department,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Helper getters
  String get roleDisplayName {
    if (role == null) return 'Member';
    return role!.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1).toLowerCase()
    ).join(' ');
  }

  String get positionDisplayName {
    if (position == null) return roleDisplayName;
    return position!;
  }

  bool get isCurrentlyActive {
    if (!isActive) return false;
    
    final now = DateTime.now();
    
    // Check start date
    if (startDate != null && startDate!.isAfter(now)) {
      return false;
    }
    
    // Check end date
    if (endDate != null && endDate!.isBefore(now)) {
      return false;
    }
    
    return true;
  }

  String get statusText {
    if (!isActive) return 'Inactive';
    if (!isCurrentlyActive) return 'Expired';
    return 'Active';
  }

  String get durationText {
    if (startDate == null) return 'No start date';
    
    final now = DateTime.now();
    final start = startDate!;
    
    if (endDate != null) {
      final end = endDate!;
      final duration = end.difference(start).inDays;
      return '${duration} days';
    } else {
      final duration = now.difference(start).inDays;
      return '${duration} days (ongoing)';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyMember &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PropertyMember{id: $id, propertyId: $propertyId, userId: $userId, role: $role, position: $position, isActive: $isActive}';
  }
}
