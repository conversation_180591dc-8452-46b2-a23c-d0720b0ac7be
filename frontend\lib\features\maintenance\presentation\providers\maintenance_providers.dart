import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/maintenance_issue.dart';
import '../../data/maintenance_api_service.dart';
import '../../data/maintenance_repository_impl.dart';
import '../../domain/maintenance_repository.dart';

// API Service Provider
final maintenanceApiServiceProvider = Provider<MaintenanceApiService>((ref) {
  return MaintenanceApiService(DioClient.instance.dio);
});

// Repository Provider
final maintenanceRepositoryProvider = Provider<MaintenanceRepository>((ref) {
  final apiService = ref.read(maintenanceApiServiceProvider);
  return MaintenanceRepositoryImpl(apiService);
});

// Maintenance Issues State
class MaintenanceIssuesState {
  final List<MaintenanceIssue> issues;
  final bool isLoading;
  final String? error;

  const MaintenanceIssuesState({
    this.issues = const [],
    this.isLoading = false,
    this.error,
  });

  MaintenanceIssuesState copyWith({
    List<MaintenanceIssue>? issues,
    bool? isLoading,
    String? error,
  }) {
    return MaintenanceIssuesState(
      issues: issues ?? this.issues,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Maintenance Issues Notifier
class MaintenanceIssuesNotifier extends StateNotifier<MaintenanceIssuesState> {
  final MaintenanceRepository _repository;

  MaintenanceIssuesNotifier(this._repository) : super(const MaintenanceIssuesState()) {
    loadIssues();
  }

  Future<void> loadIssues() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final issues = await _repository.getMaintenanceIssues();
      state = state.copyWith(
        issues: issues,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> addIssue(MaintenanceIssue issue) async {
    try {
      final newIssue = await _repository.createMaintenanceIssue(issue);
      state = state.copyWith(
        issues: [newIssue, ...state.issues],
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<void> updateIssue(MaintenanceIssue issue) async {
    try {
      final updatedIssue = await _repository.updateMaintenanceIssue(issue);
      final updatedIssues = state.issues.map((i) {
        return i.id == updatedIssue.id ? updatedIssue : i;
      }).toList();

      state = state.copyWith(issues: updatedIssues);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<void> deleteIssue(String issueId) async {
    try {
      await _repository.deleteMaintenanceIssue(issueId);
      final updatedIssues = state.issues
          .where((i) => i.id != issueId)
          .toList();

      state = state.copyWith(issues: updatedIssues);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Maintenance Issues Provider
final maintenanceIssuesNotifierProvider = StateNotifierProvider<MaintenanceIssuesNotifier, MaintenanceIssuesState>((ref) {
  final repository = ref.read(maintenanceRepositoryProvider);
  return MaintenanceIssuesNotifier(repository);
});

// Convenience providers
final maintenanceIssuesProvider = Provider<AsyncValue<List<MaintenanceIssue>>>((ref) {
  final state = ref.watch(maintenanceIssuesNotifierProvider);

  if (state.isLoading && state.issues.isEmpty) {
    return const AsyncValue.loading();
  } else if (state.error != null) {
    return AsyncValue.error(state.error!, StackTrace.current);
  } else {
    return AsyncValue.data(state.issues);
  }
});

final maintenanceIssuesLoadingProvider = Provider<bool>((ref) {
  return ref.watch(maintenanceIssuesNotifierProvider).isLoading;
});

final maintenanceIssuesErrorProvider = Provider<String?>((ref) {
  return ref.watch(maintenanceIssuesNotifierProvider).error;
});

// Issue by ID provider
final maintenanceIssueByIdProvider = Provider.family<MaintenanceIssue?, String>((ref, issueId) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  try {
    return issues.firstWhere((issue) => issue.id == issueId);
  } catch (e) {
    return null;
  }
});

// Filtered issues providers
final openIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.isOpen).toList();
});

final inProgressIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.isInProgress).toList();
});

final resolvedIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.isResolved).toList();
});

final closedIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.isClosed).toList();
});

// Priority-based providers
final criticalIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.isCriticalPriority).toList();
});

final highPriorityIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.isHighPriority).toList();
});

final overdueIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.isOverdue).toList();
});

final escalationRequiredIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.requiresEscalation).toList();
});

final unassignedIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => !issue.isAssigned).toList();
});

// Statistics provider
final maintenanceStatsProvider = Provider<Map<String, int>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;

  return {
    'total': issues.length,
    'open': issues.where((i) => i.isOpen).length,
    'in_progress': issues.where((i) => i.isInProgress).length,
    'resolved': issues.where((i) => i.isResolved).length,
    'closed': issues.where((i) => i.isClosed).length,
    'critical': issues.where((i) => i.isCriticalPriority).length,
    'high': issues.where((i) => i.isHighPriority).length,
    'overdue': issues.where((i) => i.isOverdue).length,
    'escalation_required': issues.where((i) => i.requiresEscalation).length,
    'unassigned': issues.where((i) => !i.isAssigned).length,
  };
});

// Issues by property provider
final issuesByPropertyProvider = Provider.family<List<MaintenanceIssue>, String>((ref, propertyId) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.propertyId == propertyId).toList();
});

// Issues by service type provider
final issuesByServiceTypeProvider = Provider.family<List<MaintenanceIssue>, String>((ref, serviceType) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  return issues.where((issue) => issue.serviceType == serviceType).toList();
});

// Search provider
final maintenanceSearchProvider = Provider.family<List<MaintenanceIssue>, String>((ref, query) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;

  if (query.isEmpty) return issues;

  final lowercaseQuery = query.toLowerCase();
  return issues.where((issue) {
    return issue.title.toLowerCase().contains(lowercaseQuery) ||
           issue.description.toLowerCase().contains(lowercaseQuery) ||
           (issue.serviceType?.toLowerCase().contains(lowercaseQuery) ?? false) ||
           (issue.department?.toLowerCase().contains(lowercaseQuery) ?? false);
  }).toList();
});

// Property-based maintenance issues provider
final maintenanceIssuesByPropertyProvider = FutureProvider.family<List<MaintenanceIssue>, String>((ref, propertyId) async {
  final repository = ref.read(maintenanceRepositoryProvider);
  return repository.getMaintenanceIssuesByProperty(propertyId);
});

// Property-filtered maintenance issues provider (with access control)
final propertyFilteredMaintenanceProvider = FutureProvider<List<MaintenanceIssue>>((ref) async {
  final repository = ref.read(maintenanceRepositoryProvider);
  final allIssues = await repository.getMaintenanceIssues();

  // TODO: Apply property access filtering here
  // For now, return all issues
  return allIssues;
});

// Recent issues provider (last 7 days)
final recentIssuesProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;
  final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));

  return issues.where((issue) => issue.createdAt.isAfter(sevenDaysAgo)).toList()
    ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
});

// Issues requiring attention provider
final issuesRequiringAttentionProvider = Provider<List<MaintenanceIssue>>((ref) {
  final issues = ref.watch(maintenanceIssuesNotifierProvider).issues;

  return issues.where((issue) {
    return issue.isCriticalPriority ||
           issue.isOverdue ||
           issue.requiresEscalation ||
           (issue.isOpen && !issue.isAssigned);
  }).toList()
    ..sort((a, b) {
      // Sort by priority: critical > overdue > escalation > unassigned
      if (a.isCriticalPriority && !b.isCriticalPriority) return -1;
      if (!a.isCriticalPriority && b.isCriticalPriority) return 1;
      if (a.isOverdue && !b.isOverdue) return -1;
      if (!a.isOverdue && b.isOverdue) return 1;
      if (a.requiresEscalation && !b.requiresEscalation) return -1;
      if (!a.requiresEscalation && b.requiresEscalation) return 1;
      return b.createdAt.compareTo(a.createdAt);
    });
});
