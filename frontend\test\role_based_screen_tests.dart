import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

// Import core auth models and providers
import 'package:srsr_property_management/core/auth/models/user_role.dart' as core_models;
import 'package:srsr_property_management/features/auth/presentation/providers/auth_providers.dart';

import 'package:srsr_property_management/core/auth/widgets/role_based_widget.dart';


// Import API services
import 'package:srsr_property_management/features/auth/data/auth_api_service.dart';
import 'package:srsr_property_management/features/admin/data/role_management_api_service.dart';
import 'package:srsr_property_management/features/admin/data/user_management_api_service.dart';

// Import models
import 'package:srsr_property_management/shared/models/user.dart';
import 'package:srsr_property_management/shared/models/api_response.dart';

void main() {
  group('Role-Based Screen Tests', () {
    late Dio dio;
    late AuthApiService authService;
    late RoleManagementApiService roleService;
    late UserManagementApiService userService;
    late ProviderContainer container;

    // Test data
    String? adminToken;
    String? managerToken;
    String? viewerToken;
    User? adminUser;
    User? managerUser;
    User? viewerUser;

    setUpAll(() async {
      // Configure Dio for real API calls
      dio = Dio(BaseOptions(
        baseUrl: 'http://192.168.1.3:3000',
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {'Content-Type': 'application/json'},
      ));

      // Add logging interceptor
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => print('[ROLE_TEST] $obj'),
      ));

      // Initialize services
      authService = AuthApiService(dio);
      roleService = RoleManagementApiService(dio);
      userService = UserManagementApiService(dio);
    });

    setUp(() {
      // Create fresh provider container for each test
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Test Data Setup', () {
      test('authenticate with existing admin or create test admin', () async {
        try {
          // First try to authenticate with existing admin
          final loginRequest = LoginRequest(
            email: '<EMAIL>',
            password: 'admin123',
          );

          final result = await authService.login(loginRequest);
          adminToken = result.token;
          adminUser = result.user;

          print('✅ Admin authenticated: ${adminUser!.fullName}');
        } catch (e) {
          print('⚠️ Admin login failed, will create test admin: $e');

          // If admin doesn't exist, create one for testing
          try {
            final createRequest = CreateUserRequest(
              email: '<EMAIL>',
              fullName: 'Test Admin User',
              password: 'testadmin123',
              roles: ['admin'],
            );

            await userService.createUser(createRequest);

            // Now authenticate with the created admin
            final loginRequest = LoginRequest(
              email: '<EMAIL>',
              password: 'testadmin123',
            );

            final result = await authService.login(loginRequest);
            adminToken = result.token;
            adminUser = result.user;

            print('✅ Test admin created and authenticated: ${adminUser!.fullName}');
          } catch (createError) {
            fail('❌ Failed to create test admin: $createError');
          }
        }
      });

      test('create test roles with specific permissions', () async {
        if (adminToken == null) {
          fail('❌ Admin authentication required');
        }

        // Set auth header for subsequent requests
        dio.options.headers['Authorization'] = 'Bearer $adminToken';

        try {
          // Create test manager role
          final managerRoleRequest = CreateRoleRequest(
            name: 'Test_Manager',
            description: 'Test manager role for testing',
            permissions: [
              'properties.read',
              'properties.create',
              'properties.update',
              'maintenance.read',
              'maintenance.create',
              'maintenance.update',
              'attendance.read',
              'fuel.read',
            ],
          );

          final managerRoleResult = await roleService.createRole(managerRoleRequest);
          expect(managerRoleResult.success, true);

          // Create test viewer role
          final viewerRoleRequest = CreateRoleRequest(
            name: 'Test_Viewer',
            description: 'Test viewer role for testing',
            permissions: [
              'properties.read',
              'maintenance.read',
              'attendance.read',
              'fuel.read',
            ],
          );

          final viewerRoleResult = await roleService.createRole(viewerRoleRequest);
          expect(viewerRoleResult.success, true);

          print('✅ Test roles created successfully');
        } catch (e) {
          print('⚠️ Role creation failed (may already exist): $e');
        }
      });

      test('create test users with specific roles', () async {
        if (adminToken == null) {
          fail('❌ Admin authentication required');
        }

        try {
          // Create test manager user
          final managerUserRequest = CreateUserRequest(
            email: '<EMAIL>',
            fullName: 'Test Manager User',
            password: 'testmanager123',
            roles: ['Test_Manager'],
          );

          await userService.createUser(managerUserRequest);

          // Authenticate manager
          final managerLoginRequest = LoginRequest(
            email: '<EMAIL>',
            password: 'testmanager123',
          );

          final managerResult = await authService.login(managerLoginRequest);
          managerToken = managerResult.token;
          managerUser = managerResult.user;

          // Create test viewer user
          final viewerUserRequest = CreateUserRequest(
            email: '<EMAIL>',
            fullName: 'Test Viewer User',
            password: 'testviewer123',
            roles: ['Test_Viewer'],
          );

          await userService.createUser(viewerUserRequest);

          // Authenticate viewer
          final viewerLoginRequest = LoginRequest(
            email: '<EMAIL>',
            password: 'testviewer123',
          );

          final viewerResult = await authService.login(viewerLoginRequest);
          viewerToken = viewerResult.token;
          viewerUser = viewerResult.user;

          print('✅ Test users created and authenticated');
          print('   Manager: ${managerUser!.fullName}');
          print('   Viewer: ${viewerUser!.fullName}');
        } catch (e) {
          print('⚠️ Test user creation failed (may already exist): $e');

          // Try to authenticate with existing test users
          try {
            final managerLoginRequest = LoginRequest(
              email: '<EMAIL>',
              password: 'testmanager123',
            );
            final managerResult = await authService.login(managerLoginRequest);
            managerToken = managerResult.token;
            managerUser = managerResult.user;

            final viewerLoginRequest = LoginRequest(
              email: '<EMAIL>',
              password: 'testviewer123',
            );
            final viewerResult = await authService.login(viewerLoginRequest);
            viewerToken = viewerResult.token;
            viewerUser = viewerResult.user;

            print('✅ Authenticated with existing test users');
          } catch (authError) {
            print('⚠️ Could not authenticate test users: $authError');
          }
        }
      });
    });

    group('Role-Based Widget Access Tests', () {
      testWidgets('Admin role should have access to all widgets', (WidgetTester tester) async {
        if (adminUser == null) {
          markTestSkipped('Admin user not available');
          return;
        }

        // Override auth state with admin user
        final authStateOverride = AuthState(
          isAuthenticated: true,
          user: adminUser!,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => AuthNotifier(ref.read(authRepositoryProvider))..state = authStateOverride),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    // Test admin-only widget
                    RoleBasedWidget(
                      allowedRoles: const [core_models.UserRole.admin],
                      child: const Text('Admin Only Content'),
                      fallback: const Text('Access Denied'),
                    ),
                    // Test permission-based widget
                    RoleBasedWidget(
                      requiredPermissions: const ['users.create'],
                      child: const Text('Create User Button'),
                      fallback: const Text('No Permission'),
                    ),
                    // Test properties access
                    RoleBasedWidget(
                      requiredPermissions: const ['properties.read'],
                      child: const Text('Properties Access'),
                      fallback: const Text('No Properties Access'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Admin should see all content
        expect(find.text('Admin Only Content'), findsOneWidget);
        expect(find.text('Create User Button'), findsOneWidget);
        expect(find.text('Properties Access'), findsOneWidget);

        // Should not see fallback content
        expect(find.text('Access Denied'), findsNothing);
        expect(find.text('No Permission'), findsNothing);
        expect(find.text('No Properties Access'), findsNothing);

        print('✅ Admin role access test passed');
      });

      testWidgets('Manager role should have limited access', (WidgetTester tester) async {
        if (managerUser == null) {
          markTestSkipped('Manager user not available');
          return;
        }

        // Override auth state with manager user
        final authStateOverride = AuthState(
          isAuthenticated: true,
          user: managerUser!,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => AuthNotifier(ref.read(authRepositoryProvider))..state = authStateOverride),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    // Test admin-only widget (should be denied)
                    RoleBasedWidget(
                      allowedRoles: const [core_models.UserRole.admin],
                      child: const Text('Admin Only Content'),
                      fallback: const Text('Access Denied'),
                    ),
                    // Test properties access (should be allowed)
                    RoleBasedWidget(
                      requiredPermissions: const ['properties.read'],
                      child: const Text('Properties Access'),
                      fallback: const Text('No Properties Access'),
                    ),
                    // Test user creation (should be denied)
                    RoleBasedWidget(
                      requiredPermissions: const ['users.create'],
                      child: const Text('Create User Button'),
                      fallback: const Text('No Permission'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Manager should NOT see admin content
        expect(find.text('Admin Only Content'), findsNothing);
        expect(find.text('Access Denied'), findsOneWidget);

        // Manager should see properties access
        expect(find.text('Properties Access'), findsOneWidget);
        expect(find.text('No Properties Access'), findsNothing);

        // Manager should NOT see user creation
        expect(find.text('Create User Button'), findsNothing);
        expect(find.text('No Permission'), findsOneWidget);

        print('✅ Manager role access test passed');
      });

      testWidgets('Viewer role should have minimal access', (WidgetTester tester) async {
        if (viewerUser == null) {
          markTestSkipped('Viewer user not available');
          return;
        }

        // Override auth state with viewer user
        final authStateOverride = AuthState(
          isAuthenticated: true,
          user: viewerUser!,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => AuthNotifier(ref.read(authRepositoryProvider))..state = authStateOverride),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    // Test admin-only widget (should be denied)
                    RoleBasedWidget(
                      allowedRoles: const [core_models.UserRole.admin],
                      child: const Text('Admin Only Content'),
                      fallback: const Text('Access Denied'),
                    ),
                    // Test read-only access
                    RoleBasedWidget(
                      requiredPermissions: const ['properties.read'],
                      child: const Text('Properties View'),
                      fallback: const Text('No Properties Access'),
                    ),
                    // Test creation permissions (should be denied)
                    RoleBasedWidget(
                      requiredPermissions: const ['properties.create'],
                      child: const Text('Create Property Button'),
                      fallback: const Text('No Create Permission'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Viewer should NOT see admin content
        expect(find.text('Admin Only Content'), findsNothing);
        expect(find.text('Access Denied'), findsOneWidget);

        // Viewer should see read-only content
        expect(find.text('Properties View'), findsOneWidget);
        expect(find.text('No Properties Access'), findsNothing);

        // Viewer should NOT see creation buttons
        expect(find.text('Create Property Button'), findsNothing);
        expect(find.text('No Create Permission'), findsOneWidget);

        print('✅ Viewer role access test passed');
      });
    });

    group('Screen Navigation Access Tests', () {
      test('Admin can access all screen paths', () {
        if (adminUser == null) {
          markTestSkipped('Admin user not available');
          return;
        }

        final adminRole = core_models.UserRole.admin;

        // Test all screen paths
        final screenPaths = [
          '/admin',
          '/users',
          '/roles',
          '/settings',
          '/properties',
          '/maintenance',
          '/attendance',
          '/fuel',
          '/reports',
          '/dashboard',
        ];

        for (final path in screenPaths) {
          final hasAccess = adminRole.canAccessScreen(path);
          expect(hasAccess, true, reason: 'Admin should access $path');
        }

        print('✅ Admin screen access test passed');
      });

      test('Manager has limited screen access', () {
        final managerRole = core_models.UserRole.manager;

        // Screens manager should access
        final allowedScreens = [
          '/properties',
          '/maintenance',
          '/attendance',
          '/fuel',
          '/dashboard',
        ];

        // Screens manager should NOT access
        final deniedScreens = [
          '/admin',
          '/users',
          '/roles',
          '/settings',
        ];

        for (final path in allowedScreens) {
          final hasAccess = managerRole.canAccessScreen(path);
          expect(hasAccess, true, reason: 'Manager should access $path');
        }

        for (final path in deniedScreens) {
          final hasAccess = managerRole.canAccessScreen(path);
          expect(hasAccess, false, reason: 'Manager should NOT access $path');
        }

        print('✅ Manager screen access test passed');
      });

      test('Viewer has minimal screen access', () {
        final viewerRole = core_models.UserRole.viewer;

        // Screens viewer should access
        final allowedScreens = [
          '/properties',
          '/maintenance',
          '/attendance',
          '/fuel',
          '/dashboard',
        ];

        // Screens viewer should NOT access
        final deniedScreens = [
          '/admin',
          '/users',
          '/roles',
          '/settings',
          '/reports', // Viewer cannot generate reports
        ];

        for (final path in allowedScreens) {
          final hasAccess = viewerRole.canAccessScreen(path);
          expect(hasAccess, true, reason: 'Viewer should access $path');
        }

        for (final path in deniedScreens) {
          final hasAccess = viewerRole.canAccessScreen(path);
          expect(hasAccess, false, reason: 'Viewer should NOT access $path');
        }

        print('✅ Viewer screen access test passed');
      });
    });

    group('Test Data Cleanup', () {
      test('cleanup test users and roles', () async {
        if (adminToken == null) {
          return; // Skip cleanup if no admin token
        }

        try {
          // Delete test users
          final testEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
          ];

          for (final email in testEmails) {
            try {
              // Get user by email and delete
              final users = await userService.getUsers();
              final user = users.data?.firstWhere(
                (u) => u.email == email,
                orElse: () => throw Exception('User not found'),
              );

              if (user != null) {
                await userService.deleteUser(user.id);
                print('✅ Deleted test user: $email');
              }
            } catch (e) {
              print('⚠️ Could not delete user $email: $e');
            }
          }

          // Delete test roles
          final testRoleNames = ['Test_Manager', 'Test_Viewer'];

          for (final roleName in testRoleNames) {
            try {
              final roles = await roleService.getRoles();
              final role = roles.data?.firstWhere(
                (r) => r.name == roleName,
                orElse: () => throw Exception('Role not found'),
              );

              if (role != null) {
                await roleService.deleteRole(role.id);
                print('✅ Deleted test role: $roleName');
              }
            } catch (e) {
              print('⚠️ Could not delete role $roleName: $e');
            }
          }

          print('✅ Test data cleanup completed');
        } catch (e) {
          print('⚠️ Cleanup failed: $e');
        }
      });
    });
  });
}
