import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/storage/storage_service.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;
  bool _biometricEnabled = false;
  String _selectedLanguage = 'English';
  String _refreshInterval = '30';
  bool _autoSync = true;
  bool _offlineMode = false;

  final List<String> _languages = ['English', 'Hindi', 'Tamil', 'Telugu'];
  final List<String> _refreshIntervals = ['15', '30', '60', '120'];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      setState(() {
        _notificationsEnabled = StorageService.getBool('notifications_enabled') ?? true;
        _darkModeEnabled = StorageService.getBool('dark_mode_enabled') ?? false;
        _biometricEnabled = StorageService.getBool('biometric_enabled') ?? false;
        _selectedLanguage = StorageService.getString('selected_language') ?? 'English';
        _refreshInterval = StorageService.getString('refresh_interval') ?? '30';
        _autoSync = StorageService.getBool('auto_sync') ?? true;
        _offlineMode = StorageService.getBool('offline_mode') ?? false;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to load settings: $e');
      }
    }
  }

  Future<void> _saveSettings() async {
    try {
      await StorageService.setBool('notifications_enabled', _notificationsEnabled);
      await StorageService.setBool('dark_mode_enabled', _darkModeEnabled);
      await StorageService.setBool('biometric_enabled', _biometricEnabled);
      await StorageService.setString('selected_language', _selectedLanguage);
      await StorageService.setString('refresh_interval', _refreshInterval);
      await StorageService.setBool('auto_sync', _autoSync);
      await StorageService.setBool('offline_mode', _offlineMode);

      if (mounted) {
        AppUtils.showSuccessSnackBar(context, 'Settings saved successfully');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to save settings: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authStateProvider);
    final user = authState.user;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // App Settings
          _buildSectionHeader('App Settings'),
          _buildSettingsCard([
            _buildSwitchTile(
              'Notifications',
              'Receive push notifications',
              Icons.notifications,
              _notificationsEnabled,
              (value) => setState(() => _notificationsEnabled = value),
            ),
            _buildSwitchTile(
              'Dark Mode',
              'Use dark theme',
              Icons.dark_mode,
              _darkModeEnabled,
              (value) => setState(() => _darkModeEnabled = value),
            ),
            _buildDropdownTile(
              'Language',
              'Select app language',
              Icons.language,
              _selectedLanguage,
              _languages,
              (value) => setState(() => _selectedLanguage = value!),
            ),
          ]),

          const SizedBox(height: AppConstants.largePadding),

          // Security Settings
          _buildSectionHeader('Security'),
          _buildSettingsCard([
            _buildSwitchTile(
              'Biometric Authentication',
              'Use fingerprint or face unlock',
              Icons.fingerprint,
              _biometricEnabled,
              (value) => setState(() => _biometricEnabled = value),
            ),
            _buildActionTile(
              'Change Password',
              'Update your account password',
              Icons.lock,
              () => _showChangePasswordDialog(),
            ),
            _buildActionTile(
              'Two-Factor Authentication',
              'Add extra security to your account',
              Icons.security,
              () => _showTwoFactorDialog(),
            ),
          ]),

          const SizedBox(height: AppConstants.largePadding),

          // Data & Sync Settings
          _buildSectionHeader('Data & Sync'),
          _buildSettingsCard([
            _buildSwitchTile(
              'Auto Sync',
              'Automatically sync data',
              Icons.sync,
              _autoSync,
              (value) => setState(() => _autoSync = value),
            ),
            _buildSwitchTile(
              'Offline Mode',
              'Work without internet connection',
              Icons.offline_bolt,
              _offlineMode,
              (value) => setState(() => _offlineMode = value),
            ),
            _buildDropdownTile(
              'Refresh Interval',
              'How often to refresh data (seconds)',
              Icons.refresh,
              _refreshInterval,
              _refreshIntervals,
              (value) => setState(() => _refreshInterval = value!),
            ),
          ]),

          const SizedBox(height: AppConstants.largePadding),

          // Account Settings
          _buildSectionHeader('Account'),
          _buildSettingsCard([
            _buildActionTile(
              'Profile Information',
              'Update your profile details',
              Icons.person,
              () => Navigator.of(context).pop(), // Go back to profile
            ),
            _buildActionTile(
              'Privacy Settings',
              'Manage your privacy preferences',
              Icons.privacy_tip,
              () => _showPrivacyDialog(),
            ),
            _buildActionTile(
              'Export Data',
              'Download your data',
              Icons.download,
              () => _showExportDataDialog(),
            ),
          ]),

          const SizedBox(height: AppConstants.largePadding),

          // About
          _buildSectionHeader('About'),
          _buildSettingsCard([
            _buildInfoTile(
              'App Version',
              AppConstants.appVersion,
              Icons.info,
            ),
            _buildActionTile(
              'Terms of Service',
              'Read our terms and conditions',
              Icons.description,
              () => _showTermsDialog(),
            ),
            _buildActionTile(
              'Privacy Policy',
              'Read our privacy policy',
              Icons.policy,
              () => _showPrivacyPolicyDialog(),
            ),
            _buildActionTile(
              'Contact Support',
              'Get help and support',
              Icons.support,
              () => _showSupportDialog(),
            ),
          ]),

          const SizedBox(height: AppConstants.largePadding),

          // Danger Zone
          if (user?.role?.name == 'Admin') ...[
            _buildSectionHeader('Danger Zone', color: Colors.red),
            _buildSettingsCard([
              _buildActionTile(
                'Clear Cache',
                'Clear all cached data',
                Icons.clear_all,
                () => _showClearCacheDialog(),
                textColor: Colors.orange,
              ),
              _buildActionTile(
                'Reset Settings',
                'Reset all settings to default',
                Icons.restore,
                () => _showResetSettingsDialog(),
                textColor: Colors.red,
              ),
            ]),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, {Color? color}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Card(
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      secondary: Icon(icon),
      value: value,
      onChanged: onChanged,
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    IconData icon,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<String>(
        value: value,
        onChanged: onChanged,
        items: options.map((option) {
          return DropdownMenuItem(
            value: option,
            child: Text(option),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: textColor),
      title: Text(title, style: TextStyle(color: textColor)),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(String title, String value, IconData icon) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      trailing: Text(
        value,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Colors.grey[600],
        ),
      ),
    );
  }

  void _showChangePasswordDialog() {
    // TODO: Implement change password dialog
    AppUtils.showInfoSnackBar(context, 'Change password feature coming soon');
  }

  void _showTwoFactorDialog() {
    // TODO: Implement two-factor authentication dialog
    AppUtils.showInfoSnackBar(context, 'Two-factor authentication coming soon');
  }

  void _showPrivacyDialog() {
    // TODO: Implement privacy settings dialog
    AppUtils.showInfoSnackBar(context, 'Privacy settings coming soon');
  }

  void _showExportDataDialog() {
    // TODO: Implement export data dialog
    AppUtils.showInfoSnackBar(context, 'Export data feature coming soon');
  }

  void _showTermsDialog() {
    // TODO: Implement terms of service dialog
    AppUtils.showInfoSnackBar(context, 'Terms of service coming soon');
  }

  void _showPrivacyPolicyDialog() {
    // TODO: Implement privacy policy dialog
    AppUtils.showInfoSnackBar(context, 'Privacy policy coming soon');
  }

  void _showSupportDialog() {
    // TODO: Implement support dialog
    AppUtils.showInfoSnackBar(context, 'Contact support coming soon');
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('This will clear all cached data. Are you sure?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // TODO: Implement cache clearing
              AppUtils.showSuccessSnackBar(context, 'Cache cleared successfully');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showResetSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('This will reset all settings to default values. Are you sure?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // Reset to defaults
              setState(() {
                _notificationsEnabled = true;
                _darkModeEnabled = false;
                _biometricEnabled = false;
                _selectedLanguage = 'English';
                _refreshInterval = '30';
                _autoSync = true;
                _offlineMode = false;
              });
              await _saveSettings();
              AppUtils.showSuccessSnackBar(context, 'Settings reset to default');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
