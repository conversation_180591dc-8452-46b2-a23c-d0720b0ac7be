import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/property.dart';
import '../../data/properties_api_service.dart';
import '../../data/properties_repository_impl.dart';
import '../../domain/properties_repository.dart';

// API Service Provider
final propertiesApiServiceProvider = Provider<PropertiesApiService>((ref) {
  return PropertiesApiService(DioClient.instance.dio);
});

// Repository Provider
final propertiesRepositoryProvider = Provider<PropertiesRepository>((ref) {
  final apiService = ref.read(propertiesApiServiceProvider);
  return PropertiesRepositoryImpl(apiService);
});

// Properties State
class PropertiesState {
  final List<Property> properties;
  final bool isLoading;
  final String? error;

  const PropertiesState({
    this.properties = const [],
    this.isLoading = false,
    this.error,
  });

  PropertiesState copyWith({
    List<Property>? properties,
    bool? isLoading,
    String? error,
  }) {
    return PropertiesState(
      properties: properties ?? this.properties,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Properties Notifier
class PropertiesNotifier extends StateNotifier<PropertiesState> {
  final PropertiesRepository _repository;

  PropertiesNotifier(this._repository) : super(const PropertiesState()) {
    loadProperties();
  }

  Future<void> loadProperties() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final properties = await _repository.getProperties();
      state = state.copyWith(
        properties: properties,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> addProperty(Property property) async {
    try {
      final newProperty = await _repository.createProperty(property);
      state = state.copyWith(
        properties: [...state.properties, newProperty],
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<void> updateProperty(Property property) async {
    try {
      final updatedProperty = await _repository.updateProperty(property);
      final updatedProperties = state.properties.map((p) {
        return p.id == updatedProperty.id ? updatedProperty : p;
      }).toList();

      state = state.copyWith(properties: updatedProperties);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<void> deleteProperty(String propertyId) async {
    try {
      await _repository.deleteProperty(propertyId);
      final updatedProperties = state.properties
          .where((p) => p.id != propertyId)
          .toList();

      state = state.copyWith(properties: updatedProperties);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Properties Provider
final propertiesNotifierProvider = StateNotifierProvider<PropertiesNotifier, PropertiesState>((ref) {
  final repository = ref.read(propertiesRepositoryProvider);
  return PropertiesNotifier(repository);
});

// Convenience providers
final propertiesProvider = Provider<AsyncValue<List<Property>>>((ref) {
  final state = ref.watch(propertiesNotifierProvider);

  if (state.isLoading && state.properties.isEmpty) {
    return const AsyncValue.loading();
  } else if (state.error != null) {
    return AsyncValue.error(state.error!, StackTrace.current);
  } else {
    return AsyncValue.data(state.properties);
  }
});

final propertiesLoadingProvider = Provider<bool>((ref) {
  return ref.watch(propertiesNotifierProvider).isLoading;
});

final propertiesErrorProvider = Provider<String?>((ref) {
  return ref.watch(propertiesNotifierProvider).error;
});

// Property by ID provider - fetches from API if not in cache
final propertyByIdProvider = FutureProvider.family<Property, String>((ref, propertyId) async {
  // First try to get from cache
  final properties = ref.watch(propertiesNotifierProvider).properties;
  try {
    final cachedProperty = properties.firstWhere((property) => property.id == propertyId);
    return cachedProperty;
  } catch (e) {
    // If not in cache, fetch from API
    final repository = ref.read(propertiesRepositoryProvider);
    return repository.getPropertyById(propertyId);
  }
});

// Filtered properties providers
final residentialPropertiesProvider = Provider<List<Property>>((ref) {
  final properties = ref.watch(propertiesNotifierProvider).properties;
  return properties.where((property) => property.isResidential).toList();
});

final officePropertiesProvider = Provider<List<Property>>((ref) {
  final properties = ref.watch(propertiesNotifierProvider).properties;
  return properties.where((property) => property.isOffice).toList();
});

final activePropertiesProvider = Provider<List<Property>>((ref) {
  final properties = ref.watch(propertiesNotifierProvider).properties;
  return properties.where((property) => property.isActive).toList();
});

final inactivePropertiesProvider = Provider<List<Property>>((ref) {
  final properties = ref.watch(propertiesNotifierProvider).properties;
  return properties.where((property) => !property.isActive).toList();
});

// Property statistics provider
final propertyStatsProvider = Provider<Map<String, int>>((ref) {
  final properties = ref.watch(propertiesNotifierProvider).properties;

  return {
    'total': properties.length,
    'residential': properties.where((p) => p.isResidential).length,
    'office': properties.where((p) => p.isOffice).length,
    'active': properties.where((p) => p.isActive).length,
    'inactive': properties.where((p) => !p.isActive).length,
    'with_services': properties.where((p) => p.hasActiveServices).length,
  };
});

// Property search provider
final propertySearchProvider = Provider.family<List<Property>, String>((ref, query) {
  final properties = ref.watch(propertiesNotifierProvider).properties;

  if (query.isEmpty) return properties;

  final lowercaseQuery = query.toLowerCase();
  return properties.where((property) {
    return property.name.toLowerCase().contains(lowercaseQuery) ||
           property.type.toLowerCase().contains(lowercaseQuery) ||
           (property.address?.toLowerCase().contains(lowercaseQuery) ?? false) ||
           (property.description?.toLowerCase().contains(lowercaseQuery) ?? false);
  }).toList();
});

// Property status providers
final criticalPropertiesProvider = Provider<List<Property>>((ref) {
  final properties = ref.watch(propertiesNotifierProvider).properties;
  return properties.where((property) {
    final status = property.calculateOverallStatus();
    return status.toString() == 'StatusLevel.red';
  }).toList();
});

final warningPropertiesProvider = Provider<List<Property>>((ref) {
  final properties = ref.watch(propertiesNotifierProvider).properties;
  return properties.where((property) {
    final status = property.calculateOverallStatus();
    return status.toString() == 'StatusLevel.orange';
  }).toList();
});

final operationalPropertiesProvider = Provider<List<Property>>((ref) {
  final properties = ref.watch(propertiesNotifierProvider).properties;
  return properties.where((property) {
    final status = property.calculateOverallStatus();
    return status.toString() == 'StatusLevel.green';
  }).toList();
});
