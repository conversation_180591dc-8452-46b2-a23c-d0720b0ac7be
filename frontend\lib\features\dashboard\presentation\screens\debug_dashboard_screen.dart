import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/widgets/stat_card.dart';

import '../providers/debug_dashboard_providers.dart';
import '../widgets/recent_alerts_widget.dart';

/// Debug version of dashboard screen without permission checks
/// Use this to test if the dashboard data displays correctly
class DebugDashboardScreen extends ConsumerWidget {
  const DebugDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardAsync = ref.watch(debugDashboardDataProvider);
    final debugInfoAsync = ref.watch(debugInfoProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Dashboard'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.refresh(debugDashboardDataProvider);
              ref.refresh(debugInfoProvider);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.refresh(debugDashboardDataProvider);
          ref.refresh(debugInfoProvider);
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Debug Info
              Card(
                color: Colors.orange.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.bug_report, color: Colors.orange.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Debug Mode',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'This dashboard bypasses permission checks to test data display. '
                        'If data shows here but not in the main dashboard, the issue is with permissions.',
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),

              // Debug Information
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Debug Information',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      debugInfoAsync.when(
                        data: (info) => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: info.entries.map((entry) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              '${entry.key}: ${entry.value}',
                              style: const TextStyle(fontSize: 12),
                            ),
                          )).toList(),
                        ),
                        loading: () => const Text('Running debug tests...'),
                        error: (error, stack) => Text(
                          'Debug tests failed: $error',
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),

              // Dashboard Content
              dashboardAsync.when(
                data: (dashboard) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Property Statistics
                    const Text(
                      'Property Overview',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: AppConstants.smallPadding,
                      mainAxisSpacing: AppConstants.smallPadding,
                      childAspectRatio: 1.2,
                      children: [
                        StatCard(
                          title: 'Total Properties',
                          value: dashboard.data.properties.total.toString(),
                          icon: Icons.business,
                          color: Colors.blue,
                          onTap: () => context.push('/properties'),
                        ),
                        StatCard(
                          title: 'Operational',
                          value: dashboard.data.properties.operational.toString(),
                          icon: Icons.check_circle,
                          color: Colors.green,
                          onTap: () => context.push('/properties?status=operational'),
                        ),
                        StatCard(
                          title: 'Warning',
                          value: dashboard.data.properties.warning.toString(),
                          icon: Icons.warning,
                          color: Colors.orange,
                          onTap: () => context.push('/properties?status=warning'),
                        ),
                        StatCard(
                          title: 'Critical',
                          value: dashboard.data.properties.critical.toString(),
                          icon: Icons.error,
                          color: Colors.red,
                          onTap: () => context.push('/properties?status=critical'),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.largePadding),

                    // Maintenance Statistics
                    const Text(
                      'Maintenance Issues',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: AppConstants.smallPadding,
                      mainAxisSpacing: AppConstants.smallPadding,
                      childAspectRatio: 1.2,
                      children: [
                        StatCard(
                          title: 'Total Issues',
                          value: dashboard.data.maintenanceIssues.total.toString(),
                          icon: Icons.build,
                          color: Colors.blue,
                          onTap: () => context.push('/maintenance'),
                        ),
                        StatCard(
                          title: 'Open',
                          value: dashboard.data.maintenanceIssues.open.toString(),
                          icon: Icons.pending,
                          color: Colors.orange,
                          onTap: () => context.push('/maintenance?status=open'),
                        ),
                        StatCard(
                          title: 'In Progress',
                          value: dashboard.data.maintenanceIssues.inProgress.toString(),
                          icon: Icons.engineering,
                          color: Colors.blue,
                          onTap: () => context.push('/maintenance?status=in_progress'),
                        ),
                        StatCard(
                          title: 'Critical',
                          value: dashboard.data.maintenanceIssues.critical.toString(),
                          icon: Icons.priority_high,
                          color: Colors.red,
                          onTap: () => context.push('/maintenance?priority=critical'),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.largePadding),

                    // Recent Alerts
                    const Text(
                      'Recent Alerts',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    RecentAlertsWidget(alerts: dashboard.data.recentAlerts),

                    // Add bottom padding to prevent overflow with bottom navigation
                    const SizedBox(height: 80),
                  ],
                ),
                loading: () => const Center(
                  child: Padding(
                    padding: EdgeInsets.all(AppConstants.largePadding),
                    child: CircularProgressIndicator(),
                  ),
                ),
                error: (error, stack) => Center(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red,
                        ),
                        const SizedBox(height: AppConstants.defaultPadding),
                        Text(
                          'Error loading dashboard',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: AppConstants.smallPadding),
                        Text(
                          error.toString(),
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppConstants.defaultPadding),
                        ElevatedButton(
                          onPressed: () {
                            ref.refresh(debugDashboardDataProvider);
                            ref.refresh(debugInfoProvider);
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
