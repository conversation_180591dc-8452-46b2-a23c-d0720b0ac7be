import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../auth/providers/permission_providers.dart';

/// Intelligent navigation that pre-checks permissions before showing routes
class IntelligentNavigator {
  static final Map<String, RoutePermission> _routePermissions = {
    '/properties': RoutePermission(permissions: ['properties.read']),
    '/properties/create': RoutePermission(permissions: ['properties.create']),
    '/properties/edit': RoutePermission(permissions: ['properties.update']),
    '/maintenance': RoutePermission(permissions: ['maintenance.read']),
    '/maintenance/create': RoutePermission(permissions: ['maintenance.create']),
    '/maintenance/edit': RoutePermission(permissions: ['maintenance.update']),
    '/users': RoutePermission(permissions: ['users.read']),
    '/users/create': RoutePermission(permissions: ['users.create']),
    '/users/edit': RoutePermission(permissions: ['users.update']),
    '/admin': RoutePermission(permissions: ['users.manage', 'roles.manage'], requireAll: false),
    '/admin/roles': RoutePermission(permissions: ['roles.manage']),
    '/attendance': RoutePermission(permissions: ['attendance.read']),
    '/fuel': RoutePermission(permissions: ['fuel.read']),
    '/reports': RoutePermission(permissions: ['reports.read']),
    '/profile': RoutePermission(permissions: ['profile.read']),
    '/settings': RoutePermission(permissions: ['settings.read']),
    '/security': RoutePermission(permissions: ['security.read']),
  };

  /// Navigate to a route only if user has permission
  static Future<bool> navigateIfAuthorized(
    BuildContext context,
    WidgetRef ref,
    String routeName, {
    Object? arguments,
    bool showUnauthorizedMessage = true,
  }) async {
    final hasPermission = await checkRoutePermission(ref, routeName);

    if (!context.mounted) return false;

    if (hasPermission) {
      Navigator.pushNamed(context, routeName, arguments: arguments);
      return true;
    } else {
      if (showUnauthorizedMessage) {
        _showUnauthorizedMessage(context, routeName);
      }
      return false;
    }
  }

  /// Replace current route only if user has permission
  static Future<bool> replaceIfAuthorized(
    BuildContext context,
    WidgetRef ref,
    String routeName, {
    Object? arguments,
    bool showUnauthorizedMessage = true,
  }) async {
    final hasPermission = await checkRoutePermission(ref, routeName);

    if (!context.mounted) return false;

    if (hasPermission) {
      Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
      return true;
    } else {
      if (showUnauthorizedMessage) {
        _showUnauthorizedMessage(context, routeName);
      }
      return false;
    }
  }

  /// Check if user has permission for a specific route
  static Future<bool> checkRoutePermission(WidgetRef ref, String routeName) async {
    final routePermission = _routePermissions[routeName];
    if (routePermission == null) {
      // If no permission is defined, allow access
      return true;
    }

    final permissionChecker = ref.read(permissionCheckerProvider);

    if (routePermission.requireAll) {
      return await permissionChecker.hasAllPermissions(routePermission.permissions);
    } else {
      return await permissionChecker.hasAnyPermission(routePermission.permissions);
    }
  }

  /// Get all accessible routes for current user
  static Future<List<String>> getAccessibleRoutes(WidgetRef ref) async {
    final accessibleRoutes = <String>[];

    for (final entry in _routePermissions.entries) {
      final hasPermission = await checkRoutePermission(ref, entry.key);
      if (hasPermission) {
        accessibleRoutes.add(entry.key);
      }
    }

    return accessibleRoutes;
  }

  /// Show unauthorized message
  static void _showUnauthorizedMessage(BuildContext context, String routeName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('You don\'t have permission to access ${_getRouteDisplayName(routeName)}'),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Get display name for route
  static String _getRouteDisplayName(String routeName) {
    switch (routeName) {
      case '/properties':
        return 'Properties';
      case '/properties/create':
        return 'Create Property';
      case '/maintenance':
        return 'Maintenance';
      case '/maintenance/create':
        return 'Create Maintenance Issue';
      case '/users':
        return 'User Management';
      case '/admin':
        return 'Administration';
      case '/attendance':
        return 'Attendance';
      case '/fuel':
        return 'Fuel Monitoring';
      case '/reports':
        return 'Reports';
      case '/profile':
        return 'Profile';
      case '/settings':
        return 'Settings';
      case '/security':
        return 'Security';
      default:
        return routeName.replaceAll('/', '').replaceAll('_', ' ');
    }
  }

  /// Register a new route with permissions
  static void registerRoute(String routeName, RoutePermission permission) {
    _routePermissions[routeName] = permission;
  }

  /// Remove route permission
  static void unregisterRoute(String routeName) {
    _routePermissions.remove(routeName);
  }
}

/// Route permission configuration
class RoutePermission {
  final List<String> permissions;
  final bool requireAll;

  const RoutePermission({
    required this.permissions,
    this.requireAll = true,
  });
}

/// Intelligent navigation button that only shows if user has permission
class IntelligentNavigationButton extends ConsumerWidget {
  final String routeName;
  final Widget child;
  final Object? arguments;
  final VoidCallback? onUnauthorized;
  final ButtonStyle? style;

  const IntelligentNavigationButton({
    super.key,
    required this.routeName,
    required this.child,
    this.arguments,
    this.onUnauthorized,
    this.style,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<bool>(
      future: IntelligentNavigator.checkRoutePermission(ref, routeName),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink(); // Don't show while checking
        }

        final hasPermission = snapshot.data ?? false;

        if (!hasPermission) {
          return const SizedBox.shrink(); // Don't show if no permission
        }

        return ElevatedButton(
          style: style,
          onPressed: () async {
            final success = await IntelligentNavigator.navigateIfAuthorized(
              context,
              ref,
              routeName,
              arguments: arguments,
              showUnauthorizedMessage: false, // We already checked
            );

            if (!success && onUnauthorized != null) {
              onUnauthorized!();
            }
          },
          child: child,
        );
      },
    );
  }
}

/// Intelligent list tile for navigation
class IntelligentNavigationTile extends ConsumerWidget {
  final String routeName;
  final Widget title;
  final Widget? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final Object? arguments;

  const IntelligentNavigationTile({
    super.key,
    required this.routeName,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.arguments,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<bool>(
      future: IntelligentNavigator.checkRoutePermission(ref, routeName),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink(); // Don't show while checking
        }

        final hasPermission = snapshot.data ?? false;

        if (!hasPermission) {
          return const SizedBox.shrink(); // Don't show if no permission
        }

        return ListTile(
          leading: leading,
          title: title,
          subtitle: subtitle,
          trailing: trailing,
          onTap: () {
            IntelligentNavigator.navigateIfAuthorized(
              context,
              ref,
              routeName,
              arguments: arguments,
              showUnauthorizedMessage: false, // We already checked
            );
          },
        );
      },
    );
  }
}

/// Intelligent tab that only shows if user has permission
class IntelligentTab extends ConsumerWidget {
  final String routeName;
  final String text;
  final IconData? icon;

  const IntelligentTab({
    super.key,
    required this.routeName,
    required this.text,
    this.icon,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<bool>(
      future: IntelligentNavigator.checkRoutePermission(ref, routeName),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink(); // Don't show while checking
        }

        final hasPermission = snapshot.data ?? false;

        if (!hasPermission) {
          return const SizedBox.shrink(); // Don't show if no permission
        }

        return Tab(
          text: text,
          icon: icon != null ? Icon(icon) : null,
        );
      },
    );
  }
}

/// Intelligent bottom navigation bar that adapts to permissions
class IntelligentBottomNavigationBar extends ConsumerWidget {
  final List<IntelligentBottomNavItem> items;
  final int currentIndex;
  final ValueChanged<int>? onTap;

  const IntelligentBottomNavigationBar({
    super.key,
    required this.items,
    required this.currentIndex,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<List<bool>>(
      future: _checkAllPermissions(ref),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink(); // Don't show while checking
        }

        final permissions = snapshot.data ?? [];
        final visibleItems = <BottomNavigationBarItem>[];
        final indexMapping = <int, int>{}; // Maps visible index to original index

        for (int i = 0; i < items.length && i < permissions.length; i++) {
          if (permissions[i]) {
            indexMapping[visibleItems.length] = i;
            visibleItems.add(BottomNavigationBarItem(
              icon: items[i].icon,
              label: items[i].label,
            ));
          }
        }

        if (visibleItems.isEmpty) {
          return const SizedBox.shrink();
        }

        // Adjust current index based on visible items
        int adjustedCurrentIndex = 0;
        for (final entry in indexMapping.entries) {
          if (entry.value == currentIndex) {
            adjustedCurrentIndex = entry.key;
            break;
          }
        }

        return BottomNavigationBar(
          items: visibleItems,
          currentIndex: adjustedCurrentIndex.clamp(0, visibleItems.length - 1),
          onTap: (index) {
            final originalIndex = indexMapping[index];
            if (originalIndex != null && onTap != null) {
              onTap!(originalIndex);
            }
          },
          type: BottomNavigationBarType.fixed,
        );
      },
    );
  }

  Future<List<bool>> _checkAllPermissions(WidgetRef ref) async {
    final results = <bool>[];

    for (final item in items) {
      final hasPermission = await IntelligentNavigator.checkRoutePermission(ref, item.routeName);
      results.add(hasPermission);
    }

    return results;
  }
}

/// Bottom navigation item with route permission
class IntelligentBottomNavItem {
  final String routeName;
  final Widget icon;
  final String label;

  const IntelligentBottomNavItem({
    required this.routeName,
    required this.icon,
    required this.label,
  });
}
