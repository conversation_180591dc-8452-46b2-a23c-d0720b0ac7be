import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'function_process.g.dart';

@JsonSerializable()
class FunctionProcess {
  final String id;
  final String name;
  final String? description;
  final String category;
  final String priority;
  final String status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_automated')
  final bool isAutomated;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'execution_frequency')
  final String? executionFrequency;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_executed')
  final DateTime? lastExecuted;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'next_execution')
  final DateTime? nextExecution;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'success_rate')
  final double? successRate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'average_duration')
  final int? averageDuration; // in seconds
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime? updatedAt;

  const FunctionProcess({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    required this.priority,
    required this.status,
    required this.isAutomated,
    this.executionFrequency,
    this.lastExecuted,
    this.nextExecution,
    this.successRate,
    this.averageDuration,
    required this.createdAt,
    this.updatedAt,
  });

  factory FunctionProcess.fromJson(Map<String, dynamic> json) => _$FunctionProcessFromJson(json);
  Map<String, dynamic> toJson() => _$FunctionProcessToJson(this);

  FunctionProcess copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    String? priority,
    String? status,
    bool? isAutomated,
    String? executionFrequency,
    DateTime? lastExecuted,
    DateTime? nextExecution,
    double? successRate,
    int? averageDuration,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FunctionProcess(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      isAutomated: isAutomated ?? this.isAutomated,
      executionFrequency: executionFrequency ?? this.executionFrequency,
      lastExecuted: lastExecuted ?? this.lastExecuted,
      nextExecution: nextExecution ?? this.nextExecution,
      successRate: successRate ?? this.successRate,
      averageDuration: averageDuration ?? this.averageDuration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FunctionProcess &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FunctionProcess{id: $id, name: $name, category: $category, status: $status}';
  }

  // Business logic methods
  bool get isActive => status.toLowerCase() == 'active';
  bool get isInactive => status.toLowerCase() == 'inactive';
  bool get isUnderMaintenance => status.toLowerCase() == 'maintenance';

  String get statusDisplayName {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'maintenance':
        return 'Under Maintenance';
      default:
        return status;
    }
  }

  Color get statusColor {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.red;
      case 'maintenance':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String get priorityDisplayName {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'Critical';
      case 'high':
        return 'High';
      case 'medium':
        return 'Medium';
      case 'low':
        return 'Low';
      default:
        return priority;
    }
  }

  Color get priorityColor {
    switch (priority.toLowerCase()) {
      case 'critical':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.yellow;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  int get priorityValue {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 1;
      case 'high':
        return 2;
      case 'medium':
        return 3;
      case 'low':
        return 4;
      default:
        return 5;
    }
  }

  IconData get categoryIcon {
    switch (category.toLowerCase()) {
      case 'maintenance':
        return Icons.build;
      case 'monitoring':
        return Icons.monitor;
      case 'backup':
        return Icons.backup;
      case 'security':
        return Icons.security;
      case 'reporting':
        return Icons.assessment;
      case 'notification':
        return Icons.notifications;
      case 'cleanup':
        return Icons.cleaning_services;
      case 'sync':
        return Icons.sync;
      default:
        return Icons.settings;
    }
  }

  Color get categoryColor {
    switch (category.toLowerCase()) {
      case 'maintenance':
        return Colors.orange;
      case 'monitoring':
        return Colors.blue;
      case 'backup':
        return Colors.green;
      case 'security':
        return Colors.red;
      case 'reporting':
        return Colors.purple;
      case 'notification':
        return Colors.amber;
      case 'cleanup':
        return Colors.teal;
      case 'sync':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  String get categoryDisplayName {
    switch (category.toLowerCase()) {
      case 'maintenance':
        return 'Maintenance';
      case 'monitoring':
        return 'Monitoring';
      case 'backup':
        return 'Backup';
      case 'security':
        return 'Security';
      case 'reporting':
        return 'Reporting';
      case 'notification':
        return 'Notification';
      case 'cleanup':
        return 'Cleanup';
      case 'sync':
        return 'Synchronization';
      default:
        return category;
    }
  }

  String get executionTypeText => isAutomated ? 'Automated' : 'Manual';

  String get frequencyDisplayText {
    if (executionFrequency == null) return 'Not scheduled';
    switch (executionFrequency!.toLowerCase()) {
      case 'hourly':
        return 'Every hour';
      case 'daily':
        return 'Daily';
      case 'weekly':
        return 'Weekly';
      case 'monthly':
        return 'Monthly';
      case 'on-demand':
        return 'On demand';
      default:
        return executionFrequency!;
    }
  }

  String get lastExecutedText {
    if (lastExecuted == null) return 'Never executed';
    return _formatRelativeTime(lastExecuted!);
  }

  String get nextExecutionText {
    if (nextExecution == null) return 'Not scheduled';
    final now = DateTime.now();
    if (nextExecution!.isBefore(now)) return 'Overdue';
    return 'In ${_formatRelativeTime(nextExecution!, future: true)}';
  }

  String get successRateText {
    if (successRate == null) return 'No data';
    return '${(successRate! * 100).toStringAsFixed(1)}%';
  }

  Color get successRateColor {
    if (successRate == null) return Colors.grey;
    if (successRate! >= 0.95) return Colors.green;
    if (successRate! >= 0.80) return Colors.orange;
    return Colors.red;
  }

  String get averageDurationText {
    if (averageDuration == null) return 'No data';
    
    if (averageDuration! < 60) {
      return '${averageDuration!}s';
    } else if (averageDuration! < 3600) {
      final minutes = averageDuration! ~/ 60;
      final seconds = averageDuration! % 60;
      return seconds > 0 ? '${minutes}m ${seconds}s' : '${minutes}m';
    } else {
      final hours = averageDuration! ~/ 3600;
      final minutes = (averageDuration! % 3600) ~/ 60;
      return minutes > 0 ? '${hours}h ${minutes}m' : '${hours}h';
    }
  }

  bool get isOverdue {
    if (nextExecution == null) return false;
    return nextExecution!.isBefore(DateTime.now());
  }

  bool get isDueToday {
    if (nextExecution == null) return false;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final executionDate = DateTime(nextExecution!.year, nextExecution!.month, nextExecution!.day);
    return executionDate == today;
  }

  String get healthStatus {
    if (successRate == null) return 'Unknown';
    if (successRate! >= 0.95) return 'Excellent';
    if (successRate! >= 0.80) return 'Good';
    if (successRate! >= 0.60) return 'Fair';
    return 'Poor';
  }

  Color get healthStatusColor {
    if (successRate == null) return Colors.grey;
    if (successRate! >= 0.95) return Colors.green;
    if (successRate! >= 0.80) return Colors.lightGreen;
    if (successRate! >= 0.60) return Colors.orange;
    return Colors.red;
  }

  String _formatRelativeTime(DateTime dateTime, {bool future = false}) {
    final now = DateTime.now();
    final difference = future ? dateTime.difference(now) : now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'}';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'}';
    } else {
      return future ? 'Soon' : 'Just now';
    }
  }

  String get createdAtFormatted => _formatDate(createdAt);
  String get updatedAtFormatted => updatedAt != null ? _formatDate(updatedAt!) : 'Never';

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  // Validation methods
  bool get isValid => name.isNotEmpty && category.isNotEmpty && priority.isNotEmpty && status.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    if (name.isEmpty) errors.add('Name is required');
    if (category.isEmpty) errors.add('Category is required');
    if (priority.isEmpty) errors.add('Priority is required');
    if (status.isEmpty) errors.add('Status is required');
    
    const validStatuses = ['active', 'inactive', 'maintenance'];
    if (!validStatuses.contains(status.toLowerCase())) {
      errors.add('Invalid status');
    }
    
    const validPriorities = ['critical', 'high', 'medium', 'low'];
    if (!validPriorities.contains(priority.toLowerCase())) {
      errors.add('Invalid priority');
    }
    
    if (successRate != null && (successRate! < 0 || successRate! > 1)) {
      errors.add('Success rate must be between 0 and 1');
    }
    
    if (averageDuration != null && averageDuration! < 0) {
      errors.add('Average duration cannot be negative');
    }
    
    return errors;
  }

  // Summary text for cards/lists
  String get summaryText {
    final parts = <String>[];
    parts.add(categoryDisplayName);
    parts.add(priorityDisplayName);
    
    if (isAutomated) {
      parts.add('Automated');
    }
    
    if (successRate != null) {
      parts.add('${successRateText} success');
    }
    
    return parts.join(' • ');
  }
}
