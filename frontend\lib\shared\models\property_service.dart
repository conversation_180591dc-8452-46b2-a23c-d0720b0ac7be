import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';
import '../../core/business_logic/status_calculator.dart';

part 'property_service.g.dart';

@JsonSerializable()
class PropertyService {
  final String id;
  @Json<PERSON>ey(name: 'service_type')
  final String serviceType; // electricity, water, internet, security, ott
  final String status; // operational, warning, critical, maintenance
  @JsonKey(name: 'last_checked')
  final DateTime? lastChecked;
  final String? notes;

  const PropertyService({
    required this.id,
    required this.serviceType,
    required this.status,
    this.lastChecked,
    this.notes,
  });

  factory PropertyService.fromJson(Map<String, dynamic> json) => _$PropertyServiceFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyServiceToJson(this);

  PropertyService copyWith({
    String? id,
    String? serviceType,
    String? status,
    DateTime? lastChecked,
    String? notes,
  }) {
    return PropertyService(
      id: id ?? this.id,
      serviceType: serviceType ?? this.serviceType,
      status: status ?? this.status,
      lastChecked: lastChecked ?? this.lastChecked,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PropertyService && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PropertyService(id: $id, serviceType: $serviceType, status: $status, lastChecked: $lastChecked, notes: $notes)';
  }

  // Business logic methods
  bool get isActive => status.toLowerCase() == 'operational';
  bool get isWarning => status.toLowerCase() == 'warning';
  bool get isCritical => status.toLowerCase() == 'critical';
  bool get isUnderMaintenance => status.toLowerCase() == 'maintenance';

  StatusLevel get statusLevel {
    switch (status.toLowerCase()) {
      case 'operational':
        return StatusLevel.green;
      case 'warning':
        return StatusLevel.orange;
      case 'critical':
      case 'maintenance':
        return StatusLevel.red;
      default:
        return StatusLevel.orange;
    }
  }

  Color get statusColor {
    switch (statusLevel) {
      case StatusLevel.green:
        return Colors.green;
      case StatusLevel.orange:
        return Colors.orange;
      case StatusLevel.red:
        return Colors.red;
    }
  }

  IconData get serviceIcon {
    switch (serviceType.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'water':
        return Icons.water_drop;
      case 'internet':
        return Icons.wifi;
      case 'security':
        return Icons.security;
      case 'ott':
        return Icons.tv;
      case 'generator':
        return Icons.power;
      case 'maintenance':
        return Icons.build;
      default:
        return Icons.miscellaneous_services;
    }
  }

  String get displayName {
    switch (serviceType.toLowerCase()) {
      case 'electricity':
        return 'Electricity';
      case 'water':
        return 'Water Supply';
      case 'internet':
        return 'Internet';
      case 'security':
        return 'Security System';
      case 'ott':
        return 'OTT Services';
      case 'generator':
        return 'Generator';
      case 'maintenance':
        return 'Maintenance';
      default:
        return serviceType.split('_').map((word) =>
            word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }

  String get statusDisplayName {
    switch (status.toLowerCase()) {
      case 'operational':
        return 'Operational';
      case 'warning':
        return 'Warning';
      case 'critical':
        return 'Critical';
      case 'maintenance':
        return 'Under Maintenance';
      default:
        return status.split('_').map((word) =>
            word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }

  bool get isCriticalService {
    const criticalServices = ['electricity', 'water', 'security', 'generator'];
    return criticalServices.contains(serviceType.toLowerCase());
  }

  bool get requiresImmediateAttention => isCritical && isCriticalService;

  Duration? get timeSinceLastCheck {
    if (lastChecked == null) return null;
    return DateTime.now().difference(lastChecked!);
  }

  bool get isOverdueForCheck {
    if (lastChecked == null) return true;
    final daysSinceCheck = timeSinceLastCheck!.inDays;

    // Critical services should be checked more frequently
    if (isCriticalService) {
      return daysSinceCheck > 7; // Weekly for critical services
    } else {
      return daysSinceCheck > 30; // Monthly for non-critical services
    }
  }

  String get lastCheckedDescription {
    if (lastChecked == null) return 'Never checked';

    final duration = timeSinceLastCheck!;
    if (duration.inDays > 0) {
      return '${duration.inDays} days ago';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} hours ago';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  // Validation methods
  bool get isValid => serviceType.isNotEmpty && status.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    if (serviceType.isEmpty) errors.add('Service type is required');
    if (status.isEmpty) errors.add('Status is required');

    const validStatuses = ['operational', 'warning', 'critical', 'maintenance'];
    if (!validStatuses.contains(status.toLowerCase())) {
      errors.add('Invalid status');
    }

    const validServiceTypes = ['electricity', 'water', 'internet', 'security', 'ott', 'generator', 'maintenance'];
    if (!validServiceTypes.contains(serviceType.toLowerCase())) {
      errors.add('Invalid service type');
    }

    return errors;
  }

  // Helper methods for creating services
  static PropertyService createElectricityService({
    required String id,
    required String status,
    DateTime? lastChecked,
    String? notes,
  }) {
    return PropertyService(
      id: id,
      serviceType: 'electricity',
      status: status,
      lastChecked: lastChecked,
      notes: notes,
    );
  }

  static PropertyService createWaterService({
    required String id,
    required String status,
    DateTime? lastChecked,
    String? notes,
  }) {
    return PropertyService(
      id: id,
      serviceType: 'water',
      status: status,
      lastChecked: lastChecked,
      notes: notes,
    );
  }

  static PropertyService createInternetService({
    required String id,
    required String status,
    DateTime? lastChecked,
    String? notes,
  }) {
    return PropertyService(
      id: id,
      serviceType: 'internet',
      status: status,
      lastChecked: lastChecked,
      notes: notes,
    );
  }

  static PropertyService createSecurityService({
    required String id,
    required String status,
    DateTime? lastChecked,
    String? notes,
  }) {
    return PropertyService(
      id: id,
      serviceType: 'security',
      status: status,
      lastChecked: lastChecked,
      notes: notes,
    );
  }

  // Additional getters for compatibility with PropertyServiceCard
  IconData get icon => serviceIcon;
  String get lastCheckedFormatted => lastCheckedDescription;
}
