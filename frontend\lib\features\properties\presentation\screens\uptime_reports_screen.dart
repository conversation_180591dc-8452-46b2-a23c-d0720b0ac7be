import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/uptime_report.dart';
import '../providers/uptime_reports_providers.dart';
import '../widgets/uptime_report_card.dart';
import '../widgets/add_uptime_report_dialog.dart';

class UptimeReportsScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const UptimeReportsScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<UptimeReportsScreen> createState() => _UptimeReportsScreenState();
}

class _UptimeReportsScreenState extends ConsumerState<UptimeReportsScreen> {
  String _selectedFilter = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final uptimeReportsAsync = ref.watch(uptimeReportsByPropertyProvider(widget.propertyId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Uptime Reports'),
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['properties.update'],
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddUptimeReportDialog(),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(uptimeReportsByPropertyProvider(widget.propertyId)),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search uptime reports...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip('all', 'All Services'),
                      const SizedBox(width: 8),
                      _buildFilterChip('electricity', 'Electricity'),
                      const SizedBox(width: 8),
                      _buildFilterChip('internet', 'Internet'),
                      const SizedBox(width: 8),
                      _buildFilterChip('water', 'Water'),
                      const SizedBox(width: 8),
                      _buildFilterChip('generator', 'Generator'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Reports List
          Expanded(
            child: uptimeReportsAsync.when(
              data: (reports) {
                final filteredReports = _filterReports(reports);

                if (filteredReports.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(uptimeReportsByPropertyProvider(widget.propertyId));
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    itemCount: filteredReports.length,
                    itemBuilder: (context, index) {
                      final report = filteredReports[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                        child: UptimeReportCard(
                          report: report,
                          onTap: () => _showReportDetails(report),
                          onEdit: () => _showEditReportDialog(report),
                          onDelete: () => _showDeleteConfirmation(report),
                        ),
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
        ],
      ),
      floatingActionButton: RoleBasedWidget(
        requiredPermissions: const ['properties.update'],
        child: FloatingActionButton(
          onPressed: () => _showAddUptimeReportDialog(),
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  List<UptimeReport> _filterReports(List<UptimeReport> reports) {
    var filtered = reports;

    // Apply service type filter
    if (_selectedFilter != 'all') {
      filtered = filtered.where((report) {
        return report.serviceType.toLowerCase() == _selectedFilter.toLowerCase();
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((report) {
        return report.serviceType.toLowerCase().contains(query) ||
               (report.notes?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.date.compareTo(a.date));

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.trending_up,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Uptime Reports Found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != 'all'
                ? 'Try adjusting your search or filters'
                : 'Add your first uptime report to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          RoleBasedWidget(
            requiredPermissions: const ['properties.update'],
            child: ElevatedButton.icon(
              onPressed: () => _showAddUptimeReportDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add Uptime Report'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load uptime reports',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(uptimeReportsByPropertyProvider(widget.propertyId)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showAddUptimeReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AddUptimeReportDialog(
        propertyId: widget.propertyId,
        onReportAdded: () {
          ref.invalidate(uptimeReportsByPropertyProvider(widget.propertyId));
          AppUtils.showSuccessSnackBar(context, 'Uptime report added successfully');
        },
      ),
    );
  }

  void _showReportDetails(UptimeReport report) {
    // TODO: Navigate to report details screen
    AppUtils.showInfoSnackBar(context, 'Report details: ${report.serviceType}');
  }

  void _showEditReportDialog(UptimeReport report) {
    showDialog(
      context: context,
      builder: (context) => AddUptimeReportDialog(
        propertyId: widget.propertyId,
        report: report,
        onReportAdded: () {
          ref.invalidate(uptimeReportsByPropertyProvider(widget.propertyId));
          AppUtils.showSuccessSnackBar(context, 'Uptime report updated successfully');
        },
      ),
    );
  }

  void _showDeleteConfirmation(UptimeReport report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Uptime Report'),
        content: Text('Are you sure you want to delete the ${report.serviceType} uptime report for ${AppUtils.formatDate(report.date)}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(uptimeReportsNotifierProvider(widget.propertyId).notifier)
                    .deleteUptimeReport(report.id);
                AppUtils.showSuccessSnackBar(context, 'Uptime report deleted successfully');
              } catch (e) {
                AppUtils.showErrorSnackBar(context, 'Failed to delete uptime report: $e');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
